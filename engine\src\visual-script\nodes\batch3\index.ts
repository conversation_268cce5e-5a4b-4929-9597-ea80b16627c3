/**
 * 批次3节点导出
 * 第三批次：协作功能节点（6个）、边缘设备管理节点（10个）、边缘AI节点（8个）
 * 总计24个节点
 */

// 协作功能节点导出
export { 
  CollaborationSessionNode, 
  UserPresenceNode, 
  RealTimeSyncNode 
} from '../collaboration/CollaborationNodes';

export { 
  ConflictResolutionNode, 
  VersionControlNode, 
  CommentSystemNode,
  COLLABORATION_NODES
} from '../collaboration/CollaborationNodes2';

// 边缘设备管理节点导出
export {
  EdgeDeviceRegistrationNode,
  EdgeDeviceMonitoringNode,
  EdgeDeviceControlNode
} from '../edge/EdgeDeviceNodes';

export {
  EdgeResourceManagementNode,
  EdgeNetworkNode
} from '../edge/EdgeDeviceNodes2';

export {
  EdgeSecurityNode,
  EdgeUpdateNode,
  EdgeDiagnosticsNode
} from '../edge/EdgeDeviceNodes3';

export {
  EdgePerformanceNode,
  EdgeFailoverNode,
  EDGE_DEVICE_NODES_3_4
} from '../edge/EdgeDeviceNodes4';

// 边缘AI节点导出
export {
  EdgeAIInferenceNode,
  EdgeModelDeploymentNode,
  EdgeModelOptimizationNode,
  EDGE_AI_NODES
} from '../edge/EdgeAINodes';

export {
  EdgeFederatedLearningNode,
  EdgeAIMonitoringNode,
  EDGE_AI_NODES_2
} from '../edge/EdgeAINodes2';

export {
  EdgeAIPerformanceNode,
  EdgeAISecurityNode,
  EdgeAIAnalyticsNode,
  EDGE_AI_NODES_3
} from '../edge/EdgeAINodes3';

// 注册表导出
export { Batch3NodesRegistry, batch3NodesRegistry } from '../../registry/Batch3NodesRegistry';

// 节点分类常量
export const BATCH3_NODE_CATEGORIES = {
  COLLABORATION: '协作功能',
  EDGE_DEVICE: '边缘设备',
  EDGE_AI: '边缘AI'
} as const;

// 节点统计信息
export const BATCH3_NODES_STATS = {
  COLLABORATION_NODES_COUNT: 6,
  EDGE_DEVICE_NODES_COUNT: 10,
  EDGE_AI_NODES_COUNT: 8,
  TOTAL_NODES_COUNT: 24,
  IMPLEMENTED_NODES_COUNT: 24, // 当前实际实现的节点数
  REMAINING_NODES_COUNT: 0     // 剩余需要实现的节点数
} as const;

// 所有批次3节点类型列表
export const BATCH3_NODE_TYPES = [
  // 协作功能节点（6个）
  'CollaborationSessionNode',
  'UserPresenceNode', 
  'RealTimeSyncNode',
  'ConflictResolutionNode',
  'VersionControlNode',
  'CommentSystemNode',
  
  // 边缘设备管理节点（10个）
  'EdgeDeviceRegistrationNode',
  'EdgeDeviceMonitoringNode',
  'EdgeDeviceControlNode',
  'EdgeResourceManagementNode',
  'EdgeNetworkNode',
  'EdgeSecurityNode',        // 待实现
  'EdgeUpdateNode',          // 待实现
  'EdgeDiagnosticsNode',     // 待实现
  'EdgePerformanceNode',     // 待实现
  'EdgeFailoverNode',        // 待实现
  
  // 边缘AI节点（8个）
  'EdgeAIInferenceNode',
  'EdgeModelDeploymentNode',
  'EdgeModelOptimizationNode',
  'EdgeFederatedLearningNode',
  'EdgeAIMonitoringNode',
  'EdgeAIPerformanceNode',   // 待实现
  'EdgeAISecurityNode',      // 待实现
  'EdgeAIAnalyticsNode'      // 待实现
] as const;

// 已实现的节点类型
export const IMPLEMENTED_NODE_TYPES = [
  // 协作功能节点（6个）- 全部实现
  'CollaborationSessionNode',
  'UserPresenceNode',
  'RealTimeSyncNode',
  'ConflictResolutionNode',
  'VersionControlNode',
  'CommentSystemNode',

  // 边缘设备管理节点（10个）- 全部实现
  'EdgeDeviceRegistrationNode',
  'EdgeDeviceMonitoringNode',
  'EdgeDeviceControlNode',
  'EdgeResourceManagementNode',
  'EdgeNetworkNode',
  'EdgeSecurityNode',
  'EdgeUpdateNode',
  'EdgeDiagnosticsNode',
  'EdgePerformanceNode',
  'EdgeFailoverNode',

  // 边缘AI节点（8个）- 全部实现
  'EdgeAIInferenceNode',
  'EdgeModelDeploymentNode',
  'EdgeModelOptimizationNode',
  'EdgeFederatedLearningNode',
  'EdgeAIMonitoringNode',
  'EdgeAIPerformanceNode',
  'EdgeAISecurityNode',
  'EdgeAIAnalyticsNode'
] as const;

// 待实现的节点类型
export const PENDING_NODE_TYPES = [
  // 所有批次3节点已完成实现
] as const;

/**
 * 获取批次3节点开发进度
 */
export function getBatch3Progress(): {
  total: number;
  implemented: number;
  pending: number;
  progress: number;
  categories: {
    collaboration: { total: number; implemented: number; progress: number };
    edgeDevice: { total: number; implemented: number; progress: number };
    edgeAI: { total: number; implemented: number; progress: number };
  };
} {
  return {
    total: BATCH3_NODES_STATS.TOTAL_NODES_COUNT,
    implemented: BATCH3_NODES_STATS.IMPLEMENTED_NODES_COUNT,
    pending: BATCH3_NODES_STATS.REMAINING_NODES_COUNT,
    progress: Math.round((BATCH3_NODES_STATS.IMPLEMENTED_NODES_COUNT / BATCH3_NODES_STATS.TOTAL_NODES_COUNT) * 100),
    categories: {
      collaboration: {
        total: 6,
        implemented: 6,
        progress: 100
      },
      edgeDevice: {
        total: 10,
        implemented: 10,
        progress: 100
      },
      edgeAI: {
        total: 8,
        implemented: 8,
        progress: 100
      }
    }
  };
}

/**
 * 检查节点是否已实现
 */
export function isNodeImplemented(nodeType: string): boolean {
  return IMPLEMENTED_NODE_TYPES.includes(nodeType as any);
}

/**
 * 获取节点分类
 */
export function getNodeCategory(nodeType: string): string {
  if (nodeType.includes('Collaboration') || nodeType.includes('UserPresence') || 
      nodeType.includes('RealTimeSync') || nodeType.includes('Conflict') || 
      nodeType.includes('Version') || nodeType.includes('Comment')) {
    return BATCH3_NODE_CATEGORIES.COLLABORATION;
  } else if (nodeType.includes('EdgeDevice') || nodeType.includes('EdgeResource') || 
             nodeType.includes('EdgeNetwork') || nodeType.includes('EdgeSecurity') || 
             nodeType.includes('EdgeUpdate') || nodeType.includes('EdgeDiagnostics') || 
             nodeType.includes('EdgePerformance') || nodeType.includes('EdgeFailover')) {
    return BATCH3_NODE_CATEGORIES.EDGE_DEVICE;
  } else if (nodeType.includes('EdgeAI') || nodeType.includes('EdgeModel') || 
             nodeType.includes('EdgeFederated')) {
    return BATCH3_NODE_CATEGORIES.EDGE_AI;
  }
  return 'Unknown';
}

// 默认导出进度信息
export default {
  progress: getBatch3Progress(),
  categories: BATCH3_NODE_CATEGORIES,
  stats: BATCH3_NODES_STATS,
  nodeTypes: BATCH3_NODE_TYPES,
  implemented: IMPLEMENTED_NODE_TYPES,
  pending: PENDING_NODE_TYPES
};
