/**
 * 边缘计算节点导出文件
 * 批次3.2节点统一导出
 */

// 边缘路由节点
export {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from './EdgeRoutingNodes';

export {
  EdgeOptimizationNode,
  EdgeQoSNode
} from './EdgeRoutingNodes2';

// 云边协调节点
export {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from './CloudEdgeNodes';

export {
  DataSynchronizationNode,
  TaskDistributionNode
} from './CloudEdgeNodes2';

export {
  ResourceOptimizationNode,
  LatencyOptimizationNode
} from './CloudEdgeNodes3';

export {
  BandwidthOptimizationNode,
  CostOptimizationNode
} from './CloudEdgeNodes4';

// 5G网络节点
export {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from './FiveGNetworkNodes';

export {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from './FiveGNetworkNodes2';

export {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from './FiveGNetworkNodes3';

// 节点类型常量
export const EDGE_ROUTING_NODES = [
  'EdgeRoutingNode',
  'EdgeLoadBalancingNode',
  'EdgeCachingNode',
  'EdgeCompressionNode',
  'EdgeOptimizationNode',
  'EdgeQoSNode'
] as const;

export const CLOUD_EDGE_NODES = [
  'CloudEdgeOrchestrationNode',
  'HybridComputingNode',
  'DataSynchronizationNode',
  'TaskDistributionNode',
  'ResourceOptimizationNode',
  'LatencyOptimizationNode',
  'BandwidthOptimizationNode',
  'CostOptimizationNode'
] as const;

export const FIVE_G_NODES = [
  'FiveGConnectionNode',
  'FiveGSlicingNode',
  'FiveGQoSNode',
  'FiveGLatencyNode',
  'FiveGBandwidthNode',
  'FiveGSecurityNode',
  'FiveGMonitoringNode',
  'FiveGOptimizationNode'
] as const;

export const ALL_EDGE_NODES = [
  ...EDGE_ROUTING_NODES,
  ...CLOUD_EDGE_NODES,
  ...FIVE_G_NODES
] as const;

// 节点分类信息
export const EDGE_NODE_CATEGORIES = {
  'Edge/Routing': EDGE_ROUTING_NODES,
  'Edge/CloudEdge': CLOUD_EDGE_NODES,
  'Edge/5G': FIVE_G_NODES
} as const;

console.log('边缘计算节点模块已加载');
console.log(`边缘路由节点: ${EDGE_ROUTING_NODES.length}个`);
console.log(`云边协调节点: ${CLOUD_EDGE_NODES.length}个`);
console.log(`5G网络节点: ${FIVE_G_NODES.length}个`);
console.log(`总计: ${ALL_EDGE_NODES.length}个边缘计算节点`);
