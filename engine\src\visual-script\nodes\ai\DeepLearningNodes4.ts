/**
 * 深度学习节点 - 第四部分
 * 完成批次3.3的深度学习节点（9-15）
 */

import { VisualScriptNode } from '../../VisualScriptNode';
import { DeepLearningNode } from './DeepLearningNodes';

/**
 * 9. 嵌入层节点
 */
export class EmbeddingLayerNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/embeddingLayer';
  public static readonly NAME = '嵌入层';
  public static readonly DESCRIPTION = '词嵌入和特征嵌入层';

  constructor() {
    super(EmbeddingLayerNode.TYPE, EmbeddingLayerNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputIndices', 'array', '输入索引', []);
    this.addInput('vocabSize', 'number', '词汇表大小', 10000);
    this.addInput('embeddingDim', 'number', '嵌入维度', 300);
    this.addInput('paddingIdx', 'number', '填充索引', 0);
  }

  private setupOutputs(): void {
    this.addOutput('embeddings', 'array', '嵌入向量');
    this.addOutput('embeddingMatrix', 'array', '嵌入矩阵');
  }

  public execute(inputs: any): any {
    try {
      const inputIndices = this.getInputValue(inputs, 'inputIndices');
      const vocabSize = this.getInputValue(inputs, 'vocabSize');
      const embeddingDim = this.getInputValue(inputs, 'embeddingDim');
      const paddingIdx = this.getInputValue(inputs, 'paddingIdx');

      if (!Array.isArray(inputIndices)) {
        throw new Error('输入索引无效');
      }

      // 初始化嵌入矩阵
      const embeddingMatrix = this.initializeEmbeddingMatrix(vocabSize, embeddingDim);
      
      // 查找嵌入向量
      const embeddings = this.lookupEmbeddings(inputIndices, embeddingMatrix, paddingIdx);

      return {
        embeddings,
        embeddingMatrix,
        result: { 
          status: 'computed', 
          sequenceLength: inputIndices.length,
          embeddingDimension: embeddingDim
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        embeddings: [],
        embeddingMatrix: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '嵌入层计算失败'
      };
    }
  }

  private initializeEmbeddingMatrix(vocabSize: number, embeddingDim: number): number[][] {
    const matrix: number[][] = [];
    
    for (let i = 0; i < vocabSize; i++) {
      const embedding: number[] = [];
      for (let j = 0; j < embeddingDim; j++) {
        // 使用正态分布初始化
        embedding.push(this.randomNormal() * 0.1);
      }
      matrix.push(embedding);
    }
    
    return matrix;
  }

  private lookupEmbeddings(indices: number[], embeddingMatrix: number[][], paddingIdx: number): number[][] {
    const embeddings: number[][] = [];
    
    for (const idx of indices) {
      if (idx === paddingIdx) {
        // 填充向量为零向量
        embeddings.push(Array(embeddingMatrix[0].length).fill(0));
      } else if (idx >= 0 && idx < embeddingMatrix.length) {
        embeddings.push([...embeddingMatrix[idx]]);
      } else {
        // 未知索引使用随机向量
        embeddings.push(Array(embeddingMatrix[0].length).fill(0).map(() => this.randomNormal() * 0.01));
      }
    }
    
    return embeddings;
  }

  private randomNormal(): number {
    const u1 = Math.random();
    const u2 = Math.random();
    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  }
}

/**
 * 10. Dropout层节点
 */
export class DropoutLayerNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/dropoutLayer';
  public static readonly NAME = 'Dropout层';
  public static readonly DESCRIPTION = 'Dropout正则化层';

  constructor() {
    super(DropoutLayerNode.TYPE, DropoutLayerNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('dropoutRate', 'number', 'Dropout率', 0.5);
    this.addInput('training', 'boolean', '训练模式', true);
  }

  private setupOutputs(): void {
    this.addOutput('outputData', 'array', '输出数据');
    this.addOutput('mask', 'array', 'Dropout掩码');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const dropoutRate = this.getInputValue(inputs, 'dropoutRate');
      const training = this.getInputValue(inputs, 'training');

      if (!Array.isArray(inputData)) {
        throw new Error('输入数据无效');
      }

      if (!training) {
        // 推理模式，直接返回输入
        return {
          outputData: inputData,
          mask: Array(inputData.length).fill(1),
          result: { status: 'inference_mode', dropoutApplied: false },
          success: true,
          error: ''
        };
      }

      // 应用dropout
      const result = this.applyDropout(inputData, dropoutRate);

      return {
        outputData: result.output,
        mask: result.mask,
        result: { 
          status: 'training_mode', 
          dropoutApplied: true,
          droppedUnits: result.droppedCount
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        outputData: [],
        mask: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : 'Dropout层计算失败'
      };
    }
  }

  private applyDropout(input: number[], dropoutRate: number): any {
    const output: number[] = [];
    const mask: number[] = [];
    let droppedCount = 0;
    
    const keepProb = 1 - dropoutRate;
    const scale = 1 / keepProb; // 缩放因子
    
    for (let i = 0; i < input.length; i++) {
      if (Math.random() < keepProb) {
        output.push(input[i] * scale);
        mask.push(1);
      } else {
        output.push(0);
        mask.push(0);
        droppedCount++;
      }
    }
    
    return { output, mask, droppedCount };
  }
}

/**
 * 11. 批量归一化节点
 */
export class BatchNormalizationNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/batchNormalization';
  public static readonly NAME = '批量归一化';
  public static readonly DESCRIPTION = '批量归一化层';

  constructor() {
    super(BatchNormalizationNode.TYPE, BatchNormalizationNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('gamma', 'array', '缩放参数', []);
    this.addInput('beta', 'array', '偏移参数', []);
    this.addInput('epsilon', 'number', '数值稳定性参数', 1e-5);
    this.addInput('momentum', 'number', '动量参数', 0.9);
  }

  private setupOutputs(): void {
    this.addOutput('normalizedData', 'array', '归一化数据');
    this.addOutput('mean', 'number', '批次均值');
    this.addOutput('variance', 'number', '批次方差');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const gamma = this.getInputValue(inputs, 'gamma');
      const beta = this.getInputValue(inputs, 'beta');
      const epsilon = this.getInputValue(inputs, 'epsilon');
      const momentum = this.getInputValue(inputs, 'momentum');

      if (!Array.isArray(inputData) || inputData.length === 0) {
        throw new Error('输入数据无效');
      }

      // 计算批次统计量
      const mean = this.computeMean(inputData);
      const variance = this.computeVariance(inputData, mean);
      
      // 归一化
      const normalized = this.normalize(inputData, mean, variance, epsilon);
      
      // 缩放和偏移
      const output = this.scaleAndShift(normalized, gamma, beta);

      return {
        normalizedData: output,
        mean,
        variance,
        result: { 
          status: 'computed', 
          batchSize: inputData.length,
          mean,
          variance
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        normalizedData: [],
        mean: 0,
        variance: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '批量归一化计算失败'
      };
    }
  }

  private computeMean(data: number[]): number {
    return data.reduce((sum, val) => sum + val, 0) / data.length;
  }

  private computeVariance(data: number[], mean: number): number {
    const squaredDiffs = data.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / data.length;
  }

  private normalize(data: number[], mean: number, variance: number, epsilon: number): number[] {
    const std = Math.sqrt(variance + epsilon);
    return data.map(val => (val - mean) / std);
  }

  private scaleAndShift(normalized: number[], gamma: number[], beta: number[]): number[] {
    return normalized.map((val, i) => {
      const g = gamma && gamma[i] !== undefined ? gamma[i] : 1;
      const b = beta && beta[i] !== undefined ? beta[i] : 0;
      return val * g + b;
    });
  }
}

/**
 * 12. 激活函数节点
 */
export class ActivationFunctionNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/activationFunction';
  public static readonly NAME = '激活函数';
  public static readonly DESCRIPTION = '各种激活函数实现';

  constructor() {
    super(ActivationFunctionNode.TYPE, ActivationFunctionNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('activationType', 'string', '激活函数类型', 'relu');
    this.addInput('alpha', 'number', '参数alpha', 0.01);
  }

  private setupOutputs(): void {
    this.addOutput('activatedData', 'array', '激活后数据');
    this.addOutput('derivative', 'array', '导数');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const activationType = this.getInputValue(inputs, 'activationType');
      const alpha = this.getInputValue(inputs, 'alpha');

      if (!Array.isArray(inputData)) {
        throw new Error('输入数据无效');
      }

      const result = this.applyActivation(inputData, activationType, alpha);

      return {
        activatedData: result.output,
        derivative: result.derivative,
        result: { 
          status: 'computed', 
          activationType,
          inputSize: inputData.length
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        activatedData: [],
        derivative: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '激活函数计算失败'
      };
    }
  }

  private applyActivation(input: number[], type: string, alpha: number): any {
    const output: number[] = [];
    const derivative: number[] = [];

    for (const x of input) {
      switch (type) {
        case 'relu':
          output.push(Math.max(0, x));
          derivative.push(x > 0 ? 1 : 0);
          break;
        case 'leaky_relu':
          output.push(x > 0 ? x : alpha * x);
          derivative.push(x > 0 ? 1 : alpha);
          break;
        case 'sigmoid':
          const sig = 1 / (1 + Math.exp(-x));
          output.push(sig);
          derivative.push(sig * (1 - sig));
          break;
        case 'tanh':
          const tanh = Math.tanh(x);
          output.push(tanh);
          derivative.push(1 - tanh * tanh);
          break;
        case 'swish':
          const swish = x / (1 + Math.exp(-x));
          output.push(swish);
          derivative.push(swish + (1 / (1 + Math.exp(-x))) * (1 - swish));
          break;
        case 'gelu':
          const gelu = 0.5 * x * (1 + Math.tanh(Math.sqrt(2 / Math.PI) * (x + 0.044715 * Math.pow(x, 3))));
          output.push(gelu);
          derivative.push(0.5 * (1 + Math.tanh(Math.sqrt(2 / Math.PI) * (x + 0.044715 * Math.pow(x, 3)))));
          break;
        default:
          output.push(x);
          derivative.push(1);
      }
    }

    return { output, derivative };
  }
}
