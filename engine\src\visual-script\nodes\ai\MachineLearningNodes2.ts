/**
 * 机器学习节点 - 第二部分
 * 继续实现批次3.3的机器学习节点（3-6）
 */

import { VisualScriptNode } from '../../VisualScriptNode';
import { MachineLearningNode } from './MachineLearningNodes';

/**
 * 3. 迁移学习节点
 */
export class TransferLearningNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/transferLearning';
  public static readonly NAME = '迁移学习';
  public static readonly DESCRIPTION = '迁移学习模型适配';

  constructor() {
    super(TransferLearningNode.TYPE, TransferLearningNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('sourceModel', 'object', '源模型', {});
    this.addInput('targetData', 'array', '目标数据', []);
    this.addInput('transferMethod', 'string', '迁移方法', 'fine_tuning');
    this.addInput('frozenLayers', 'array', '冻结层', []);
    this.addInput('learningRate', 'number', '学习率', 0.001);
    this.addInput('adaptationLayers', 'array', '适配层配置', []);
  }

  private setupOutputs(): void {
    this.addOutput('adaptedModel', 'object', '适配后模型');
    this.addOutput('transferMetrics', 'object', '迁移指标');
    this.addOutput('adaptationLoss', 'number', '适配损失');
  }

  public execute(inputs: any): any {
    try {
      const sourceModel = this.getInputValue(inputs, 'sourceModel');
      const targetData = this.getInputValue(inputs, 'targetData');
      const transferMethod = this.getInputValue(inputs, 'transferMethod');
      const frozenLayers = this.getInputValue(inputs, 'frozenLayers');
      const learningRate = this.getInputValue(inputs, 'learningRate');
      const adaptationLayers = this.getInputValue(inputs, 'adaptationLayers');

      if (!sourceModel || !targetData) {
        throw new Error('源模型或目标数据无效');
      }

      // 执行迁移学习
      const transferResult = this.performTransferLearning(
        sourceModel, targetData, transferMethod, frozenLayers, learningRate, adaptationLayers
      );

      return {
        adaptedModel: transferResult.model,
        transferMetrics: transferResult.metrics,
        adaptationLoss: transferResult.loss,
        result: { 
          status: 'transferred', 
          method: transferMethod,
          frozenLayerCount: frozenLayers.length,
          adaptationLayerCount: adaptationLayers.length
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        adaptedModel: null,
        transferMetrics: {},
        adaptationLoss: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '迁移学习失败'
      };
    }
  }

  private performTransferLearning(
    sourceModel: any, targetData: any[], method: string, 
    frozenLayers: string[], learningRate: number, adaptationLayers: any[]
  ): any {
    switch (method) {
      case 'fine_tuning':
        return this.fineTuning(sourceModel, targetData, frozenLayers, learningRate);
      case 'feature_extraction':
        return this.featureExtraction(sourceModel, targetData, adaptationLayers);
      case 'domain_adaptation':
        return this.domainAdaptation(sourceModel, targetData, learningRate);
      default:
        return this.fineTuning(sourceModel, targetData, frozenLayers, learningRate);
    }
  }

  private fineTuning(sourceModel: any, targetData: any[], frozenLayers: string[], learningRate: number): any {
    // 模拟微调过程
    const adaptedModel = JSON.parse(JSON.stringify(sourceModel)); // 深拷贝
    
    // 标记冻结层
    if (adaptedModel.layers) {
      adaptedModel.layers.forEach((layer: any, index: number) => {
        layer.frozen = frozenLayers.includes(layer.name || `layer_${index}`);
      });
    }

    // 模拟训练过程
    const epochs = 10;
    let loss = 1.0;
    const lossHistory: number[] = [];

    for (let epoch = 0; epoch < epochs; epoch++) {
      // 简化的损失计算
      loss = loss * (0.9 + Math.random() * 0.1);
      lossHistory.push(loss);
      
      // 更新非冻结层的参数
      if (adaptedModel.parameters) {
        for (let i = 0; i < adaptedModel.parameters.length; i++) {
          if (!this.isParameterFrozen(i, frozenLayers)) {
            adaptedModel.parameters[i] += (Math.random() - 0.5) * learningRate;
          }
        }
      }
    }

    const metrics = {
      method: 'fine_tuning',
      epochs,
      finalLoss: loss,
      lossHistory,
      frozenLayerCount: frozenLayers.length,
      learningRate
    };

    return { model: adaptedModel, metrics, loss };
  }

  private featureExtraction(sourceModel: any, targetData: any[], adaptationLayers: any[]): any {
    // 特征提取方法：冻结源模型，只训练新的分类层
    const adaptedModel = JSON.parse(JSON.stringify(sourceModel));
    
    // 添加适配层
    if (!adaptedModel.adaptationLayers) {
      adaptedModel.adaptationLayers = [];
    }
    
    adaptationLayers.forEach(layerConfig => {
      adaptedModel.adaptationLayers.push({
        ...layerConfig,
        parameters: this.initializeLayerParameters(layerConfig)
      });
    });

    // 提取特征
    const features = this.extractFeatures(sourceModel, targetData);
    
    // 训练适配层
    const trainingResult = this.trainAdaptationLayers(features, adaptationLayers);

    const metrics = {
      method: 'feature_extraction',
      extractedFeatureCount: features.length,
      adaptationLayerCount: adaptationLayers.length,
      trainingAccuracy: trainingResult.accuracy
    };

    return { model: adaptedModel, metrics, loss: trainingResult.loss };
  }

  private domainAdaptation(sourceModel: any, targetData: any[], learningRate: number): any {
    // 域适应方法
    const adaptedModel = JSON.parse(JSON.stringify(sourceModel));
    
    // 添加域判别器
    adaptedModel.domainDiscriminator = {
      layers: [
        { type: 'dense', size: 128, activation: 'relu' },
        { type: 'dense', size: 1, activation: 'sigmoid' }
      ],
      parameters: this.initializeDomainDiscriminator()
    };

    // 模拟对抗训练
    const epochs = 15;
    let domainLoss = 1.0;
    let classificationLoss = 1.0;

    for (let epoch = 0; epoch < epochs; epoch++) {
      // 更新特征提取器（最小化分类损失，最大化域损失）
      classificationLoss *= 0.95;
      domainLoss *= 1.02; // 域判别器变得更难区分

      // 更新域判别器（最小化域损失）
      domainLoss *= 0.98;
    }

    const totalLoss = classificationLoss + domainLoss;

    const metrics = {
      method: 'domain_adaptation',
      epochs,
      classificationLoss,
      domainLoss,
      totalLoss
    };

    return { model: adaptedModel, metrics, loss: totalLoss };
  }

  private isParameterFrozen(paramIndex: number, frozenLayers: string[]): boolean {
    // 简化的参数冻结检查
    return frozenLayers.length > 0 && paramIndex < frozenLayers.length * 10;
  }

  private extractFeatures(model: any, data: any[]): number[][] {
    // 模拟特征提取
    return data.map(sample => {
      return Array(256).fill(0).map(() => Math.random()); // 256维特征向量
    });
  }

  private trainAdaptationLayers(features: number[][], layerConfigs: any[]): any {
    // 模拟适配层训练
    let loss = 1.0;
    let accuracy = 0.5;

    for (let epoch = 0; epoch < 20; epoch++) {
      loss *= 0.95;
      accuracy = Math.min(0.95, accuracy + 0.02);
    }

    return { loss, accuracy };
  }

  private initializeLayerParameters(layerConfig: any): number[] {
    const paramCount = layerConfig.inputSize * layerConfig.outputSize + layerConfig.outputSize;
    return Array(paramCount).fill(0).map(() => (Math.random() - 0.5) * 0.1);
  }

  private initializeDomainDiscriminator(): number[] {
    return Array(128 * 128 + 128 + 128 * 1 + 1).fill(0).map(() => (Math.random() - 0.5) * 0.1);
  }
}

/**
 * 4. 模型集成节点
 */
export class ModelEnsembleNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/modelEnsemble';
  public static readonly NAME = '模型集成';
  public static readonly DESCRIPTION = '多模型集成和投票';

  constructor() {
    super(ModelEnsembleNode.TYPE, ModelEnsembleNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('models', 'array', '模型列表', []);
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('ensembleMethod', 'string', '集成方法', 'voting');
    this.addInput('weights', 'array', '模型权重', []);
    this.addInput('votingType', 'string', '投票类型', 'hard');
  }

  private setupOutputs(): void {
    this.addOutput('ensemblePrediction', 'array', '集成预测');
    this.addOutput('individualPredictions', 'array', '个体预测');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('agreementScore', 'number', '一致性得分');
  }

  public execute(inputs: any): any {
    try {
      const models = this.getInputValue(inputs, 'models');
      const inputData = this.getInputValue(inputs, 'inputData');
      const ensembleMethod = this.getInputValue(inputs, 'ensembleMethod');
      const weights = this.getInputValue(inputs, 'weights');
      const votingType = this.getInputValue(inputs, 'votingType');

      if (!Array.isArray(models) || models.length === 0) {
        throw new Error('模型列表无效');
      }

      if (!Array.isArray(inputData)) {
        throw new Error('输入数据无效');
      }

      // 获取个体预测
      const individualPredictions = this.getIndividualPredictions(models, inputData);
      
      // 执行集成
      const ensembleResult = this.performEnsemble(
        individualPredictions, ensembleMethod, weights, votingType
      );

      return {
        ensemblePrediction: ensembleResult.prediction,
        individualPredictions,
        confidence: ensembleResult.confidence,
        agreementScore: ensembleResult.agreement,
        result: { 
          status: 'computed', 
          modelCount: models.length,
          ensembleMethod,
          votingType
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        ensemblePrediction: [],
        individualPredictions: [],
        confidence: 0,
        agreementScore: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '模型集成失败'
      };
    }
  }

  private getIndividualPredictions(models: any[], inputData: any[]): any[] {
    return models.map((model, index) => {
      // 模拟模型预测
      return this.simulateModelPrediction(model, inputData, index);
    });
  }

  private simulateModelPrediction(model: any, inputData: any[], modelIndex: number): any {
    // 简化的模型预测模拟
    const prediction = inputData.map((_, i) => {
      // 基于模型索引和数据索引生成不同的预测
      const base = Math.sin(modelIndex + i) * 0.5 + 0.5;
      return Math.round(base + (Math.random() - 0.5) * 0.2);
    });

    const confidence = 0.7 + Math.random() * 0.3; // 0.7-1.0之间的置信度

    return {
      modelId: model.id || `model_${modelIndex}`,
      prediction,
      confidence,
      probabilities: prediction.map(p => p === 1 ? confidence : 1 - confidence)
    };
  }

  private performEnsemble(predictions: any[], method: string, weights: number[], votingType: string): any {
    switch (method) {
      case 'voting':
        return this.votingEnsemble(predictions, votingType, weights);
      case 'averaging':
        return this.averagingEnsemble(predictions, weights);
      case 'stacking':
        return this.stackingEnsemble(predictions);
      case 'boosting':
        return this.boostingEnsemble(predictions, weights);
      default:
        return this.votingEnsemble(predictions, votingType, weights);
    }
  }

  private votingEnsemble(predictions: any[], votingType: string, weights: number[]): any {
    if (predictions.length === 0) {
      return { prediction: [], confidence: 0, agreement: 0 };
    }

    const sampleCount = predictions[0].prediction.length;
    const ensemblePrediction: number[] = [];
    let totalAgreement = 0;

    for (let i = 0; i < sampleCount; i++) {
      if (votingType === 'hard') {
        // 硬投票
        const votes = predictions.map(p => p.prediction[i]);
        const voteCount = this.countVotes(votes);
        const winner = this.getMajorityVote(voteCount);
        ensemblePrediction.push(winner);
        
        // 计算一致性
        const maxVotes = Math.max(...Object.values(voteCount));
        totalAgreement += maxVotes / predictions.length;
      } else {
        // 软投票
        let weightedSum = 0;
        let totalWeight = 0;
        
        for (let j = 0; j < predictions.length; j++) {
          const weight = weights && weights[j] ? weights[j] : 1;
          const prob = predictions[j].probabilities[i];
          weightedSum += prob * weight;
          totalWeight += weight;
        }
        
        const avgProb = weightedSum / totalWeight;
        ensemblePrediction.push(avgProb > 0.5 ? 1 : 0);
        totalAgreement += Math.abs(avgProb - 0.5) * 2; // 转换为0-1范围
      }
    }

    const avgAgreement = totalAgreement / sampleCount;
    const confidence = this.calculateEnsembleConfidence(predictions, ensemblePrediction);

    return {
      prediction: ensemblePrediction,
      confidence,
      agreement: avgAgreement
    };
  }

  private averagingEnsemble(predictions: any[], weights: number[]): any {
    if (predictions.length === 0) {
      return { prediction: [], confidence: 0, agreement: 0 };
    }

    const sampleCount = predictions[0].prediction.length;
    const ensemblePrediction: number[] = [];
    
    for (let i = 0; i < sampleCount; i++) {
      let weightedSum = 0;
      let totalWeight = 0;
      
      for (let j = 0; j < predictions.length; j++) {
        const weight = weights && weights[j] ? weights[j] : 1;
        weightedSum += predictions[j].prediction[i] * weight;
        totalWeight += weight;
      }
      
      ensemblePrediction.push(weightedSum / totalWeight);
    }

    const confidence = this.calculateEnsembleConfidence(predictions, ensemblePrediction);
    const agreement = this.calculateAgreement(predictions);

    return { prediction: ensemblePrediction, confidence, agreement };
  }

  private stackingEnsemble(predictions: any[]): any {
    // 简化的堆叠集成
    const metaLearnerWeights = predictions.map(() => Math.random());
    const totalWeight = metaLearnerWeights.reduce((sum, w) => sum + w, 0);
    const normalizedWeights = metaLearnerWeights.map(w => w / totalWeight);

    return this.averagingEnsemble(predictions, normalizedWeights);
  }

  private boostingEnsemble(predictions: any[], weights: number[]): any {
    // 简化的提升集成
    const adaptiveWeights = weights || predictions.map((p, i) => 1 / (i + 1));
    return this.averagingEnsemble(predictions, adaptiveWeights);
  }

  private countVotes(votes: number[]): { [key: number]: number } {
    const count: { [key: number]: number } = {};
    for (const vote of votes) {
      count[vote] = (count[vote] || 0) + 1;
    }
    return count;
  }

  private getMajorityVote(voteCount: { [key: number]: number }): number {
    let maxVotes = 0;
    let winner = 0;
    
    for (const [vote, count] of Object.entries(voteCount)) {
      if (count > maxVotes) {
        maxVotes = count;
        winner = parseInt(vote);
      }
    }
    
    return winner;
  }

  private calculateEnsembleConfidence(predictions: any[], ensemblePrediction: number[]): number {
    // 基于个体模型置信度的加权平均
    const avgConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length;
    return Math.min(1.0, avgConfidence * 1.1); // 集成通常比个体更可信
  }

  private calculateAgreement(predictions: any[]): number {
    if (predictions.length < 2) return 1.0;

    const sampleCount = predictions[0].prediction.length;
    let totalAgreement = 0;

    for (let i = 0; i < sampleCount; i++) {
      const votes = predictions.map(p => p.prediction[i]);
      const uniqueVotes = new Set(votes).size;
      const agreement = 1 - (uniqueVotes - 1) / (predictions.length - 1);
      totalAgreement += agreement;
    }

    return totalAgreement / sampleCount;
  }
}
