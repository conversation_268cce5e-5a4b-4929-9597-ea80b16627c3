/**
 * 5G网络节点测试
 * 测试5GConnectionNode、5GSlicingNode、5GQoSNode、5GLatencyNode等
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from '../../../../src/visual-script/nodes/edge/FiveGNetworkNodes';
import {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from '../../../../src/visual-script/nodes/edge/FiveGNetworkNodes2';
import {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from '../../../../src/visual-script/nodes/edge/FiveGNetworkNodes3';

describe('FiveGNetworkNodes', () => {
  describe('FiveGConnectionNode', () => {
    let node: FiveGConnectionNode;

    beforeEach(() => {
      node = new FiveGConnectionNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GConnectionNode');
      expect(node.name).toBe('5G连接');
      expect(node.category).toBe('5G网络');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确建立5G连接', async () => {
      const inputs = {
        deviceInfo: {
          supportedNetworkTypes: ['5G'],
          fiveGCapabilities: { eMBB: true, URLLC: true },
          location: { latitude: 39.9042, longitude: 116.4074 }
        },
        connectionType: 'eMBB',
        networkSlice: 'default',
        qosRequirements: { minBandwidth: 100, maxLatency: 50 }
      };

      const result = await node.execute(inputs);

      expect(result.connectionStatus).toBeDefined();
      expect(result.networkMetrics).toBeDefined();
      expect(result.connectionId).toBeDefined();
      expect(result.connectionStatus.state).toBe('connected');
      expect(result.connectionStatus.connectionType).toBe('eMBB');
    });

    it('应该检测设备兼容性', async () => {
      const inputs = {
        deviceInfo: {
          supportedNetworkTypes: ['4G'], // 不支持5G
          location: { latitude: 39.9042, longitude: 116.4074 }
        },
        connectionType: 'eMBB',
        networkSlice: 'default',
        qosRequirements: {}
      };

      await expect(node.execute(inputs)).rejects.toThrow('设备不支持5G网络');
    });

    it('应该支持不同的连接类型', async () => {
      const deviceInfo = {
        supportedNetworkTypes: ['5G'],
        fiveGCapabilities: { eMBB: true, URLLC: true, mMTC: true },
        location: { latitude: 39.9042, longitude: 116.4074 }
      };

      const connectionTypes = ['eMBB', 'URLLC', 'mMTC'];

      for (const type of connectionTypes) {
        const result = await node.execute({
          deviceInfo,
          connectionType: type,
          networkSlice: 'default',
          qosRequirements: {}
        });

        expect(result.connectionStatus.connectionType).toBe(type);
      }
    });
  });

  describe('FiveGSlicingNode', () => {
    let node: FiveGSlicingNode;

    beforeEach(() => {
      node = new FiveGSlicingNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GSlicingNode');
      expect(node.name).toBe('5G网络切片');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确创建网络切片', async () => {
      const inputs = {
        sliceRequirements: {
          bandwidth: 100,
          maxLatency: 10,
          reliability: 99.9,
          maxConnections: 1000
        },
        networkResources: {
          availableBandwidth: 1000,
          minLatency: 1,
          reliability: 99.99,
          maxConnections: 10000
        },
        sliceType: 'eMBB',
        tenantInfo: { id: 'tenant1', name: 'Test Tenant' }
      };

      const result = await node.execute(inputs);

      expect(result.sliceId).toBeDefined();
      expect(result.sliceConfiguration).toBeDefined();
      expect(result.resourceAllocation).toBeDefined();
      expect(result.sliceId).toMatch(/^slice_eMBB_/);
    });

    it('应该检查资源可用性', async () => {
      const inputs = {
        sliceRequirements: {
          bandwidth: 2000 // 超过可用带宽
        },
        networkResources: {
          availableBandwidth: 1000
        },
        sliceType: 'eMBB',
        tenantInfo: {}
      };

      await expect(node.execute(inputs)).rejects.toThrow('资源不足');
    });

    it('应该为不同切片类型分配不同资源', async () => {
      const baseInputs = {
        sliceRequirements: { bandwidth: 100 },
        networkResources: { availableBandwidth: 1000, minLatency: 1 },
        tenantInfo: {}
      };

      const sliceTypes = ['eMBB', 'URLLC', 'mMTC'];

      for (const type of sliceTypes) {
        const result = await node.execute({
          ...baseInputs,
          sliceType: type
        });

        expect(result.resourceAllocation).toBeDefined();
        expect(result.sliceConfiguration.sliceType).toBe(type);
      }
    });
  });

  describe('FiveGQoSNode', () => {
    let node: FiveGQoSNode;

    beforeEach(() => {
      node = new FiveGQoSNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GQoSNode');
      expect(node.name).toBe('5G服务质量');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确管理QoS', async () => {
      const inputs = {
        serviceRequirements: {
          maxLatency: 20,
          minBandwidth: 50,
          reliability: 99.5,
          priority: 'high'
        },
        networkConditions: {
          currentLatency: 15,
          currentThroughput: 80,
          utilization: 60,
          signalStrength: -70
        },
        qosPolicy: 'adaptive',
        trafficProfile: { type: 'real_time' }
      };

      const result = await node.execute(inputs);

      expect(result.qosConfiguration).toBeDefined();
      expect(result.qosMetrics).toBeDefined();
      expect(result.qosStatus).toBeDefined();
      expect(['optimal', 'acceptable', 'degraded', 'critical']).toContain(result.qosStatus);
    });

    it('应该正确映射优先级', async () => {
      const priorities = ['critical', 'high', 'normal', 'low', 'background'];

      for (const priority of priorities) {
        const result = await node.execute({
          serviceRequirements: { priority },
          networkConditions: {},
          qosPolicy: 'adaptive',
          trafficProfile: {}
        });

        expect(result.qosConfiguration).toBeDefined();
      }
    });
  });

  describe('FiveGLatencyNode', () => {
    let node: FiveGLatencyNode;

    beforeEach(() => {
      node = new FiveGLatencyNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GLatencyNode');
      expect(node.name).toBe('5G延迟管理');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确优化延迟', async () => {
      const inputs = {
        latencyRequirements: {
          targetLatency: 10,
          maxLatency: 20,
          maxJitter: 2
        },
        networkPath: [
          { nodeId: 'node1', latency: 5, type: 'edge' },
          { nodeId: 'node2', latency: 15, type: 'cloud' }
        ],
        trafficType: 'real_time',
        optimizationMode: 'aggressive'
      };

      const result = await node.execute(inputs);

      expect(result.latencyOptimization).toBeDefined();
      expect(result.pathOptimization).toBeDefined();
      expect(result.latencyMetrics).toBeDefined();
      expect(result.latencyOptimization.expectedReduction).toBeGreaterThan(0);
    });

    it('应该根据流量类型调整要求', async () => {
      const baseInputs = {
        latencyRequirements: { targetLatency: 50 },
        networkPath: [{ nodeId: 'node1', latency: 10 }],
        optimizationMode: 'balanced'
      };

      const trafficTypes = ['ultra_low_latency', 'real_time', 'interactive', 'general'];

      for (const type of trafficTypes) {
        const result = await node.execute({
          ...baseInputs,
          trafficType: type
        });

        expect(result.latencyOptimization).toBeDefined();
      }
    });
  });

  describe('FiveGBandwidthNode', () => {
    let node: FiveGBandwidthNode;

    beforeEach(() => {
      node = new FiveGBandwidthNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GBandwidthNode');
      expect(node.name).toBe('5G带宽管理');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确管理带宽', async () => {
      const inputs = {
        bandwidthRequirements: {
          minimumBandwidth: 50,
          targetBandwidth: 100
        },
        networkCapacity: {
          totalBandwidth: 1000,
          availableBandwidth: 800,
          currentUtilization: 20
        },
        trafficDemand: [
          { service: 'video', bandwidth: 200, priority: 'high' },
          { service: 'data', bandwidth: 100, priority: 'normal' }
        ],
        allocationPolicy: 'priority_based'
      };

      const result = await node.execute(inputs);

      expect(result.bandwidthAllocation).toBeDefined();
      expect(result.trafficShaping).toBeDefined();
      expect(result.utilizationMetrics).toBeDefined();
      expect(result.bandwidthAllocation.totalAllocated).toBeGreaterThan(0);
    });

    it('应该支持不同的分配策略', async () => {
      const baseInputs = {
        bandwidthRequirements: {},
        networkCapacity: { totalBandwidth: 1000, availableBandwidth: 800 },
        trafficDemand: [
          { service: 'service1', bandwidth: 100 },
          { service: 'service2', bandwidth: 200 }
        ]
      };

      const policies = ['priority_based', 'fair_share', 'proportional', 'guaranteed_minimum'];

      for (const policy of policies) {
        const result = await node.execute({
          ...baseInputs,
          allocationPolicy: policy
        });

        expect(result.bandwidthAllocation).toBeDefined();
      }
    });
  });

  describe('FiveGSecurityNode', () => {
    let node: FiveGSecurityNode;

    beforeEach(() => {
      node = new FiveGSecurityNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GSecurityNode');
      expect(node.name).toBe('5G安全');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确管理安全', async () => {
      const inputs = {
        securityPolicy: { encryption: true, authentication: 'mutual_tls' },
        threatIntelligence: { knownThreats: [] },
        networkTraffic: {
          connections: 100,
          connectionDetails: [
            { id: 'conn1', source: '192.168.1.1', encrypted: true, protocol: 'https' }
          ]
        },
        securityLevel: 'high'
      };

      const result = await node.execute(inputs);

      expect(result.securityStatus).toBeDefined();
      expect(result.threatDetection).toBeDefined();
      expect(result.securityMetrics).toBeDefined();
      expect(['secure', 'warning', 'at_risk']).toContain(result.securityStatus.overall);
    });

    it('应该检测可疑连接', async () => {
      const inputs = {
        securityPolicy: {},
        threatIntelligence: {},
        networkTraffic: {
          connectionDetails: [
            { id: 'suspicious', source: '192.168.999.1', dataVolume: 2000000 } // 可疑IP和大数据量
          ]
        },
        securityLevel: 'standard'
      };

      const result = await node.execute(inputs);
      expect(result.threatDetection.threatsDetected.length).toBeGreaterThan(0);
    });
  });

  describe('FiveGMonitoringNode', () => {
    let node: FiveGMonitoringNode;

    beforeEach(() => {
      node = new FiveGMonitoringNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GMonitoringNode');
      expect(node.name).toBe('5G监控');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行监控', async () => {
      const inputs = {
        monitoringTargets: [
          { id: 'bs1', type: 'base_station' },
          { id: 'slice1', type: 'network_slice' }
        ],
        monitoringConfig: { interval: 60 },
        alertRules: [
          { id: 'rule1', target: { type: 'base_station', id: 'bs1' }, metric: 'signalStrength', threshold: -80, operator: 'less_than' }
        ],
        dashboardConfig: {}
      };

      const result = await node.execute(inputs);

      expect(result.monitoringData).toBeDefined();
      expect(result.alerts).toBeInstanceOf(Array);
      expect(result.dashboardData).toBeDefined();
      expect(result.monitoringData.timestamp).toBeDefined();
    });

    it('应该正确处理告警规则', async () => {
      const inputs = {
        monitoringTargets: [{ id: 'test', type: 'base_station' }],
        monitoringConfig: {},
        alertRules: [
          { id: 'high_latency', target: { type: 'base_station', id: 'test' }, metric: 'latency', threshold: 10, operator: 'greater_than', severity: 'high' }
        ],
        dashboardConfig: {}
      };

      const result = await node.execute(inputs);
      expect(result.alerts).toBeInstanceOf(Array);
    });
  });

  describe('FiveGOptimizationNode', () => {
    let node: FiveGOptimizationNode;

    beforeEach(() => {
      node = new FiveGOptimizationNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('5GOptimizationNode');
      expect(node.name).toBe('5G优化');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行优化', async () => {
      const inputs = {
        performanceMetrics: {
          networkMetrics: {
            bs1: { latency: 20, throughput: { uplink: 50, downlink: 200 } }
          },
          serviceMetrics: {
            service1: { latency: { average: 25 }, reliability: 99.5 }
          }
        },
        optimizationGoals: {
          targetLatency: 15,
          targetThroughput: 300
        },
        networkConfiguration: {},
        optimizationMode: 'balanced'
      };

      const result = await node.execute(inputs);

      expect(result.optimizationPlan).toBeDefined();
      expect(result.configurationChanges).toBeInstanceOf(Array);
      expect(result.expectedImprovements).toBeDefined();
      expect(result.optimizationPlan.phases).toBeInstanceOf(Array);
    });

    it('应该识别性能瓶颈', async () => {
      const inputs = {
        performanceMetrics: {
          networkMetrics: {
            slowNode: { latency: 100 } // 高延迟节点
          }
        },
        optimizationGoals: {},
        networkConfiguration: {},
        optimizationMode: 'aggressive'
      };

      const result = await node.execute(inputs);
      expect(result.optimizationPlan.phases.length).toBeGreaterThan(0);
    });

    it('应该支持不同的优化模式', async () => {
      const baseInputs = {
        performanceMetrics: {
          networkMetrics: { node1: { latency: 30 } }
        },
        optimizationGoals: {},
        networkConfiguration: {}
      };

      const modes = ['aggressive', 'balanced', 'conservative'];

      for (const mode of modes) {
        const result = await node.execute({
          ...baseInputs,
          optimizationMode: mode
        });

        expect(result.optimizationPlan).toBeDefined();
        expect(result.optimizationPlan.mode).toBe(mode);
      }
    });
  });
});
