/**
 * 云边协调节点测试
 * 测试CloudEdgeOrchestrationNode、HybridComputingNode、DataSynchronizationNode、TaskDistributionNode
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from '../../../../src/visual-script/nodes/edge/CloudEdgeNodes';
import {
  DataSynchronizationNode,
  TaskDistributionNode
} from '../../../../src/visual-script/nodes/edge/CloudEdgeNodes2';

describe('CloudEdgeNodes', () => {
  describe('CloudEdgeOrchestrationNode', () => {
    let node: CloudEdgeOrchestrationNode;

    beforeEach(() => {
      node = new CloudEdgeOrchestrationNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('CloudEdgeOrchestrationNode');
      expect(node.name).toBe('云边协调');
      expect(node.category).toBe('边缘计算');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行云边协调', async () => {
      const inputs = {
        cloudResources: [
          { id: 'cloud1', type: 'cloud', cpuCores: 16, memory: 32, bandwidth: 1000 }
        ],
        edgeNodes: [
          { id: 'edge1', type: 'edge', cpuCores: 4, memory: 8, bandwidth: 100 },
          { id: 'edge2', type: 'edge', cpuCores: 2, memory: 4, bandwidth: 50 }
        ],
        workloads: [
          { id: 'workload1', cpuRequirement: 2, memoryRequirement: 4, latencyRequirement: 10 },
          { id: 'workload2', cpuRequirement: 8, memoryRequirement: 16, latencyRequirement: 100 }
        ],
        orchestrationPolicy: { strategy: 'optimal' }
      };

      const result = await node.execute(inputs);

      expect(result.orchestrationPlan).toBeDefined();
      expect(result.resourceAllocation).toBeDefined();
      expect(result.orchestrationMetrics).toBeDefined();
      expect(result.orchestrationPlan.phases).toBeInstanceOf(Array);
    });

    it('应该正确分类工作负载', async () => {
      const inputs = {
        cloudResources: [{ id: 'cloud1', type: 'cloud', cpuCores: 16 }],
        edgeNodes: [{ id: 'edge1', type: 'edge', cpuCores: 4 }],
        workloads: [
          { id: 'rt1', latencyRequirement: 5 }, // 实时工作负载
          { id: 'compute1', cpuIntensive: true }, // 计算密集型
          { id: 'storage1', storageIntensive: true }, // 存储密集型
          { id: 'ai1', aiWorkload: true } // AI工作负载
        ],
        orchestrationPolicy: {}
      };

      const result = await node.execute(inputs);
      expect(result.resourceAllocation).toBeDefined();
    });
  });

  describe('HybridComputingNode', () => {
    let node: HybridComputingNode;

    beforeEach(() => {
      node = new HybridComputingNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('HybridComputingNode');
      expect(node.name).toBe('混合计算');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行混合计算', async () => {
      const inputs = {
        computeTask: {
          id: 'task1',
          type: 'data_processing',
          dataSize: 1000,
          cpuRequirement: 4,
          memoryRequirement: 8,
          estimatedTime: 3600
        },
        cloudCapacity: {
          cpuCores: 16,
          memory: 32,
          hasGPU: true,
          bandwidth: 1000,
          latency: 50,
          costPerHour: 1.0
        },
        edgeCapacity: {
          cpuCores: 4,
          memory: 8,
          hasGPU: false,
          bandwidth: 100,
          latency: 5,
          costPerHour: 0.5
        },
        hybridStrategy: 'optimal'
      };

      const result = await node.execute(inputs);

      expect(result.computePlan).toBeDefined();
      expect(result.taskDistribution).toBeDefined();
      expect(result.computeMetrics).toBeDefined();
      expect(result.taskDistribution.cloud).toBeInstanceOf(Array);
      expect(result.taskDistribution.edge).toBeInstanceOf(Array);
    });

    it('应该正确分解不同类型的任务', async () => {
      const mlTask = {
        id: 'ml-task',
        type: 'machine_learning',
        operation: 'training',
        cpuRequirement: 8,
        memoryRequirement: 16,
        estimatedTime: 7200
      };

      const inputs = {
        computeTask: mlTask,
        cloudCapacity: { cpuCores: 16, memory: 32, hasGPU: true },
        edgeCapacity: { cpuCores: 4, memory: 8, hasGPU: false },
        hybridStrategy: 'optimal'
      };

      const result = await node.execute(inputs);
      expect(result.taskDistribution).toBeDefined();
    });
  });

  describe('DataSynchronizationNode', () => {
    let node: DataSynchronizationNode;

    beforeEach(() => {
      node = new DataSynchronizationNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('DataSynchronizationNode');
      expect(node.name).toBe('数据同步');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行数据同步', async () => {
      const inputs = {
        sourceData: { key1: 'value1', key2: 'value2' },
        syncTargets: [
          { id: 'target1', currentData: { key1: 'old_value1' } },
          { id: 'target2', currentData: {} }
        ],
        syncStrategy: 'incremental',
        conflictResolution: 'last_write_wins'
      };

      const result = await node.execute(inputs);

      expect(result.syncResult).toBeInstanceOf(Array);
      expect(result.syncStatus).toBeDefined();
      expect(result.conflicts).toBeInstanceOf(Array);
      expect(result.syncStatus.overall).toBeDefined();
    });

    it('应该正确处理同步冲突', async () => {
      const inputs = {
        sourceData: { conflictKey: 'sourceValue' },
        syncTargets: [
          { id: 'target1', currentData: { conflictKey: 'targetValue' } }
        ],
        syncStrategy: 'incremental',
        conflictResolution: 'manual_resolution'
      };

      const result = await node.execute(inputs);
      expect(result.conflicts.length).toBeGreaterThan(0);
    });

    it('应该支持不同的同步策略', async () => {
      const inputs = {
        sourceData: { key1: 'value1' },
        syncTargets: [{ id: 'target1', currentData: {} }],
        conflictResolution: 'last_write_wins'
      };

      const strategies = ['full', 'incremental', 'delta'];
      
      for (const strategy of strategies) {
        const result = await node.execute({ ...inputs, syncStrategy: strategy });
        expect(result.syncResult).toBeDefined();
      }
    });
  });

  describe('TaskDistributionNode', () => {
    let node: TaskDistributionNode;

    beforeEach(() => {
      node = new TaskDistributionNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('TaskDistributionNode');
      expect(node.name).toBe('任务分发');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行任务分发', async () => {
      const inputs = {
        tasks: [
          { id: 'task1', priority: 'high', cpuRequirement: 2, memoryRequirement: 4 },
          { id: 'task2', priority: 'normal', cpuRequirement: 1, memoryRequirement: 2 },
          { id: 'task3', priority: 'low', cpuRequirement: 4, memoryRequirement: 8 }
        ],
        availableNodes: [
          { id: 'node1', type: 'cloud', cpuCores: 8, memory: 16, cpuUsage: 20 },
          { id: 'node2', type: 'edge', cpuCores: 4, memory: 8, cpuUsage: 30 }
        ],
        distributionPolicy: { selectionStrategy: 'balanced' },
        constraints: { allowedRegions: ['us-east-1', 'us-west-2'] }
      };

      const result = await node.execute(inputs);

      expect(result.distributionPlan).toBeDefined();
      expect(result.taskAssignments).toBeInstanceOf(Array);
      expect(result.distributionMetrics).toBeDefined();
      expect(result.distributionPlan.totalTasks).toBe(3);
    });

    it('应该按优先级排序任务', async () => {
      const inputs = {
        tasks: [
          { id: 'low', priority: 'low' },
          { id: 'high', priority: 'high' },
          { id: 'normal', priority: 'normal' }
        ],
        availableNodes: [
          { id: 'node1', type: 'cloud', cpuCores: 8, memory: 16 }
        ],
        distributionPolicy: { priorityWeight: 1.0 },
        constraints: {}
      };

      const result = await node.execute(inputs);
      expect(result.taskAssignments).toBeDefined();
    });

    it('应该处理资源不足的情况', async () => {
      const inputs = {
        tasks: [
          { id: 'big-task', cpuRequirement: 16, memoryRequirement: 32 }
        ],
        availableNodes: [
          { id: 'small-node', cpuCores: 4, memory: 8 }
        ],
        distributionPolicy: {},
        constraints: {}
      };

      const result = await node.execute(inputs);
      const unassignedTasks = result.taskAssignments.filter(a => !a.nodeId);
      expect(unassignedTasks.length).toBeGreaterThan(0);
    });

    it('应该支持不同的选择策略', async () => {
      const inputs = {
        tasks: [{ id: 'task1', cpuRequirement: 2 }],
        availableNodes: [
          { id: 'node1', cpuCores: 4, cost: 1.0, latency: 10 },
          { id: 'node2', cpuCores: 8, cost: 2.0, latency: 5 }
        ],
        constraints: {}
      };

      const strategies = ['performance', 'cost', 'latency', 'balanced'];
      
      for (const strategy of strategies) {
        const result = await node.execute({
          ...inputs,
          distributionPolicy: { selectionStrategy: strategy }
        });
        expect(result.taskAssignments).toBeDefined();
      }
    });
  });
});
