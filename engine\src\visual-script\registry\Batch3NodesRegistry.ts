/**
 * 批次3节点注册表
 * 注册协作功能节点、边缘设备管理节点、边缘AI节点到编辑器
 */

import { NodeRegistry } from './NodeRegistry';

// 导入协作功能节点
import { 
  CollaborationSessionNode, 
  UserPresenceNode, 
  RealTimeSyncNode 
} from '../nodes/collaboration/CollaborationNodes';
import { 
  ConflictResolutionNode, 
  VersionControlNode, 
  CommentSystemNode 
} from '../nodes/collaboration/CollaborationNodes2';

// 导入边缘设备管理节点
import {
  EdgeDeviceRegistrationNode,
  EdgeDeviceMonitoringNode,
  EdgeDeviceControlNode
} from '../nodes/edge/EdgeDeviceNodes';
import {
  EdgeResourceManagementNode,
  EdgeNetworkNode
} from '../nodes/edge/EdgeDeviceNodes2';
import {
  EdgeSecurityNode,
  EdgeUpdateNode,
  EdgeDiagnosticsNode
} from '../nodes/edge/EdgeDeviceNodes3';
import {
  EdgePerformanceNode,
  EdgeFailoverNode
} from '../nodes/edge/EdgeDeviceNodes4';

// 导入边缘AI节点
import {
  EdgeAIInferenceNode,
  EdgeModelDeploymentNode,
  EdgeModelOptimizationNode
} from '../nodes/edge/EdgeAINodes';
import {
  EdgeFederatedLearningNode,
  EdgeAIMonitoringNode
} from '../nodes/edge/EdgeAINodes2';
import {
  EdgeAIPerformanceNode,
  EdgeAISecurityNode,
  EdgeAIAnalyticsNode
} from '../nodes/edge/EdgeAINodes3';

/**
 * 批次3节点注册表类
 */
export class Batch3NodesRegistry {
  private static instance: Batch3NodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registeredNodes: Map<string, any> = new Map();

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): Batch3NodesRegistry {
    if (!Batch3NodesRegistry.instance) {
      Batch3NodesRegistry.instance = new Batch3NodesRegistry();
    }
    return Batch3NodesRegistry.instance;
  }

  /**
   * 注册所有批次3节点
   */
  public registerAllNodes(): void {
    this.registerCollaborationNodes();
    this.registerEdgeDeviceNodes();
    this.registerEdgeAINodes();
    
    console.log('批次3节点注册完成');
    console.log(`协作功能节点：6个`);
    console.log(`边缘设备管理节点：10个`);
    console.log(`边缘AI节点：8个`);
    console.log(`总计：24个节点`);
  }

  /**
   * 注册协作功能节点（6个）
   */
  private registerCollaborationNodes(): void {
    // 协作会话节点
    this.nodeRegistry.registerNode(
      'CollaborationSessionNode',
      CollaborationSessionNode,
      '协作功能',
      '管理协作会话的创建、加入、离开等操作',
      'collaboration',
      '#4CAF50'
    );

    // 用户在线状态节点
    this.nodeRegistry.registerNode(
      'UserPresenceNode',
      UserPresenceNode,
      '协作功能',
      '管理用户在线状态、位置信息等',
      'presence',
      '#2196F3'
    );

    // 实时同步节点
    this.nodeRegistry.registerNode(
      'RealTimeSyncNode',
      RealTimeSyncNode,
      '协作功能',
      '处理实时数据同步、操作广播等',
      'sync',
      '#FF9800'
    );

    // 冲突解决节点
    this.nodeRegistry.registerNode(
      'ConflictResolutionNode',
      ConflictResolutionNode,
      '协作功能',
      '处理协作过程中的数据冲突',
      'conflict',
      '#F44336'
    );

    // 版本控制节点
    this.nodeRegistry.registerNode(
      'VersionControlNode',
      VersionControlNode,
      '协作功能',
      '管理项目版本、分支、提交等',
      'version',
      '#9C27B0'
    );

    // 评论系统节点
    this.nodeRegistry.registerNode(
      'CommentSystemNode',
      CommentSystemNode,
      '协作功能',
      '管理协作过程中的评论、标注等',
      'comment',
      '#607D8B'
    );

    this.registeredNodes.set('collaboration', 6);
  }

  /**
   * 注册边缘设备管理节点（10个）
   */
  private registerEdgeDeviceNodes(): void {
    // 边缘设备注册节点
    this.nodeRegistry.registerNode(
      'EdgeDeviceRegistrationNode',
      EdgeDeviceRegistrationNode,
      '边缘设备',
      '管理边缘设备的注册、认证等',
      'device-register',
      '#00BCD4'
    );

    // 边缘设备监控节点
    this.nodeRegistry.registerNode(
      'EdgeDeviceMonitoringNode',
      EdgeDeviceMonitoringNode,
      '边缘设备',
      '监控边缘设备的状态、性能等',
      'device-monitor',
      '#4CAF50'
    );

    // 边缘设备控制节点
    this.nodeRegistry.registerNode(
      'EdgeDeviceControlNode',
      EdgeDeviceControlNode,
      '边缘设备',
      '控制边缘设备的操作、配置等',
      'device-control',
      '#FF5722'
    );

    // 边缘资源管理节点
    this.nodeRegistry.registerNode(
      'EdgeResourceManagementNode',
      EdgeResourceManagementNode,
      '边缘设备',
      '管理边缘设备的计算、存储、网络资源',
      'resource-management',
      '#795548'
    );

    // 边缘网络节点
    this.nodeRegistry.registerNode(
      'EdgeNetworkNode',
      EdgeNetworkNode,
      '边缘设备',
      '管理边缘设备的网络连接、路由等',
      'network',
      '#3F51B5'
    );

    // 边缘安全节点
    this.nodeRegistry.registerNode(
      'EdgeSecurityNode',
      EdgeSecurityNode,
      '边缘设备',
      '管理边缘设备的安全策略、认证、加密等',
      'security',
      '#E91E63'
    );

    // 边缘更新节点
    this.nodeRegistry.registerNode(
      'EdgeUpdateNode',
      EdgeUpdateNode,
      '边缘设备',
      '管理边缘设备的软件更新、固件升级等',
      'update',
      '#9C27B0'
    );

    // 边缘诊断节点
    this.nodeRegistry.registerNode(
      'EdgeDiagnosticsNode',
      EdgeDiagnosticsNode,
      '边缘设备',
      '诊断边缘设备的硬件和软件问题',
      'diagnostics',
      '#673AB7'
    );

    // 边缘性能节点
    this.nodeRegistry.registerNode(
      'EdgePerformanceNode',
      EdgePerformanceNode,
      '边缘设备',
      '监控和优化边缘设备性能',
      'performance',
      '#3F51B5'
    );

    // 边缘故障转移节点
    this.nodeRegistry.registerNode(
      'EdgeFailoverNode',
      EdgeFailoverNode,
      '边缘设备',
      '管理边缘设备的故障检测和自动转移',
      'failover',
      '#FF9800'
    );

    this.registeredNodes.set('edge-device', 10); // 全部10个节点已实现
  }

  /**
   * 注册边缘AI节点（8个）
   */
  private registerEdgeAINodes(): void {
    // 边缘AI推理节点
    this.nodeRegistry.registerNode(
      'EdgeAIInferenceNode',
      EdgeAIInferenceNode,
      '边缘AI',
      '在边缘设备上执行AI模型推理',
      'ai-inference',
      '#E91E63'
    );

    // 边缘模型部署节点
    this.nodeRegistry.registerNode(
      'EdgeModelDeploymentNode',
      EdgeModelDeploymentNode,
      '边缘AI',
      '在边缘设备上部署AI模型',
      'model-deploy',
      '#9C27B0'
    );

    // 边缘模型优化节点
    this.nodeRegistry.registerNode(
      'EdgeModelOptimizationNode',
      EdgeModelOptimizationNode,
      '边缘AI',
      '优化边缘设备上的AI模型性能',
      'model-optimize',
      '#673AB7'
    );

    // 边缘联邦学习节点
    this.nodeRegistry.registerNode(
      'EdgeFederatedLearningNode',
      EdgeFederatedLearningNode,
      '边缘AI',
      '在边缘设备上执行联邦学习',
      'federated-learning',
      '#3F51B5'
    );

    // 边缘AI监控节点
    this.nodeRegistry.registerNode(
      'EdgeAIMonitoringNode',
      EdgeAIMonitoringNode,
      '边缘AI',
      '监控边缘AI系统的运行状态',
      'ai-monitoring',
      '#2196F3'
    );

    // 边缘AI性能节点
    this.nodeRegistry.registerNode(
      'EdgeAIPerformanceNode',
      EdgeAIPerformanceNode,
      '边缘AI',
      '监控和优化边缘AI系统性能',
      'ai-performance',
      '#00BCD4'
    );

    // 边缘AI安全节点
    this.nodeRegistry.registerNode(
      'EdgeAISecurityNode',
      EdgeAISecurityNode,
      '边缘AI',
      '管理边缘AI系统的安全防护',
      'ai-security',
      '#4CAF50'
    );

    // 边缘AI分析节点
    this.nodeRegistry.registerNode(
      'EdgeAIAnalyticsNode',
      EdgeAIAnalyticsNode,
      '边缘AI',
      '分析边缘AI系统的运行数据和模式',
      'ai-analytics',
      '#FF5722'
    );

    this.registeredNodes.set('edge-ai', 8); // 全部8个节点已实现
  }

  /**
   * 获取已注册的节点统计
   */
  public getRegisteredNodesStats(): any {
    const stats = {
      collaboration: this.registeredNodes.get('collaboration') || 0,
      edgeDevice: this.registeredNodes.get('edge-device') || 0,
      edgeAI: this.registeredNodes.get('edge-ai') || 0
    };

    stats['total'] = stats.collaboration + stats.edgeDevice + stats.edgeAI;

    return stats;
  }

  /**
   * 获取节点分类信息
   */
  public getNodeCategories(): string[] {
    return ['协作功能', '边缘设备', '边缘AI'];
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.nodeRegistry.hasNode(nodeType);
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 协作功能节点
      'CollaborationSessionNode',
      'UserPresenceNode',
      'RealTimeSyncNode',
      'ConflictResolutionNode',
      'VersionControlNode',
      'CommentSystemNode',

      // 边缘设备管理节点
      'EdgeDeviceRegistrationNode',
      'EdgeDeviceMonitoringNode',
      'EdgeDeviceControlNode',
      'EdgeResourceManagementNode',
      'EdgeNetworkNode',
      'EdgeSecurityNode',
      'EdgeUpdateNode',
      'EdgeDiagnosticsNode',
      'EdgePerformanceNode',
      'EdgeFailoverNode',

      // 边缘AI节点
      'EdgeAIInferenceNode',
      'EdgeModelDeploymentNode',
      'EdgeModelOptimizationNode',
      'EdgeFederatedLearningNode',
      'EdgeAIMonitoringNode',
      'EdgeAIPerformanceNode',
      'EdgeAISecurityNode',
      'EdgeAIAnalyticsNode'
    ];
  }
}

// 导出单例实例
export const batch3NodesRegistry = Batch3NodesRegistry.getInstance();

// 自动注册所有节点
batch3NodesRegistry.registerAllNodes();
