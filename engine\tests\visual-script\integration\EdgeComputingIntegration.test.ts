/**
 * 边缘计算节点集成测试
 * 测试边缘路由、云边协调、5G网络节点之间的集成和协作
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { NodeRegistry } from '../../../src/visual-script/nodes/NodeRegistry';
import { Batch32NodesRegistry } from '../../../src/visual-script/registry/Batch32NodesRegistry';

// 导入所有边缘计算节点
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode
} from '../../../src/visual-script/nodes/edge/EdgeRoutingNodes';

import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from '../../../src/visual-script/nodes/edge/CloudEdgeNodes';

import {
  FiveGConnectionNode,
  FiveGSlicingNode
} from '../../../src/visual-script/nodes/edge/FiveGNetworkNodes';

describe('EdgeComputingIntegration', () => {
  let nodeRegistry: NodeRegistry;
  let batch32Registry: Batch32NodesRegistry;

  beforeEach(() => {
    nodeRegistry = NodeRegistry.getInstance();
    batch32Registry = Batch32NodesRegistry.getInstance();
  });

  describe('节点注册集成', () => {
    it('应该正确注册所有边缘计算节点', () => {
      const registeredTypes = batch32Registry.getAllRegisteredNodeTypes();
      
      expect(registeredTypes).toHaveLength(22);
      expect(registeredTypes).toContain('EdgeRoutingNode');
      expect(registeredTypes).toContain('CloudEdgeOrchestrationNode');
      expect(registeredTypes).toContain('5GConnectionNode');
    });

    it('应该正确分类节点', () => {
      const categories = batch32Registry.getNodeCategories();
      
      expect(categories['Edge/Routing']).toHaveLength(6);
      expect(categories['Edge/CloudEdge']).toHaveLength(8);
      expect(categories['Edge/5G']).toHaveLength(8);
    });

    it('应该能够创建所有注册的节点实例', () => {
      const nodeTypes = batch32Registry.getAllRegisteredNodeTypes();
      
      nodeTypes.forEach(nodeType => {
        expect(() => {
          const NodeClass = nodeRegistry.getNodeClass(nodeType);
          if (NodeClass) {
            new NodeClass();
          }
        }).not.toThrow();
      });
    });
  });

  describe('边缘路由工作流集成', () => {
    it('应该能够组合边缘路由和负载均衡节点', async () => {
      const routingNode = new EdgeRoutingNode();
      const loadBalancingNode = new EdgeLoadBalancingNode();
      const cachingNode = new EdgeCachingNode();

      // 第一步：路由决策
      const routingResult = await routingNode.execute({
        clientInfo: { location: { latitude: 39.9042, longitude: 116.4074 } },
        routingPolicy: 'latency',
        edgeNodes: [
          { nodeId: 'edge1', status: 'active', latency: 10, load: 30 },
          { nodeId: 'edge2', status: 'active', latency: 15, load: 20 }
        ],
        networkMetrics: {}
      });

      expect(routingResult.selectedNode).toBeDefined();

      // 第二步：负载均衡
      const loadBalancingResult = await loadBalancingNode.execute({
        edgeNodes: [routingResult.selectedNode],
        balancingAlgorithm: 'least_connections',
        requestInfo: {},
        healthChecks: { [routingResult.selectedNode.nodeId]: { status: 'healthy' } }
      });

      expect(loadBalancingResult.targetNode).toBeDefined();

      // 第三步：缓存检查
      const cacheResult = await cachingNode.execute({
        cacheKey: 'test-content',
        operation: 'get'
      });

      expect(cacheResult).toBeDefined();
    });

    it('应该能够处理边缘路由失败的情况', async () => {
      const routingNode = new EdgeRoutingNode();
      const loadBalancingNode = new EdgeLoadBalancingNode();

      // 模拟路由失败
      try {
        await routingNode.execute({
          clientInfo: {},
          routingPolicy: 'latency',
          edgeNodes: [], // 没有可用节点
          networkMetrics: {}
        });
      } catch (error) {
        expect(error.message).toContain('没有可用的边缘节点');
      }

      // 负载均衡应该能够处理空节点列表
      try {
        await loadBalancingNode.execute({
          edgeNodes: [],
          balancingAlgorithm: 'least_connections',
          requestInfo: {},
          healthChecks: {}
        });
      } catch (error) {
        expect(error.message).toContain('没有健康的边缘节点可用');
      }
    });
  });

  describe('云边协调工作流集成', () => {
    it('应该能够组合云边协调和混合计算节点', async () => {
      const orchestrationNode = new CloudEdgeOrchestrationNode();
      const hybridComputingNode = new HybridComputingNode();

      // 第一步：云边协调
      const orchestrationResult = await orchestrationNode.execute({
        cloudResources: [
          { id: 'cloud1', type: 'cloud', cpuCores: 16, memory: 32 }
        ],
        edgeNodes: [
          { id: 'edge1', type: 'edge', cpuCores: 4, memory: 8 }
        ],
        workloads: [
          { id: 'workload1', cpuRequirement: 2, memoryRequirement: 4 }
        ],
        orchestrationPolicy: {}
      });

      expect(orchestrationResult.orchestrationPlan).toBeDefined();

      // 第二步：混合计算
      const hybridResult = await hybridComputingNode.execute({
        computeTask: {
          id: 'task1',
          type: 'data_processing',
          cpuRequirement: 4,
          memoryRequirement: 8
        },
        cloudCapacity: { cpuCores: 16, memory: 32 },
        edgeCapacity: { cpuCores: 4, memory: 8 },
        hybridStrategy: 'optimal'
      });

      expect(hybridResult.computePlan).toBeDefined();
      expect(hybridResult.taskDistribution).toBeDefined();
    });

    it('应该能够处理资源不足的情况', async () => {
      const orchestrationNode = new CloudEdgeOrchestrationNode();

      const result = await orchestrationNode.execute({
        cloudResources: [],
        edgeNodes: [
          { id: 'edge1', type: 'edge', cpuCores: 1, memory: 1 }
        ],
        workloads: [
          { id: 'big-workload', cpuRequirement: 16, memoryRequirement: 32 }
        ],
        orchestrationPolicy: {}
      });

      expect(result.orchestrationMetrics.unallocatedWorkloads).toBeGreaterThan(0);
    });
  });

  describe('5G网络工作流集成', () => {
    it('应该能够组合5G连接和网络切片节点', async () => {
      const connectionNode = new FiveGConnectionNode();
      const slicingNode = new FiveGSlicingNode();

      // 第一步：建立5G连接
      const connectionResult = await connectionNode.execute({
        deviceInfo: {
          supportedNetworkTypes: ['5G'],
          fiveGCapabilities: { eMBB: true }
        },
        connectionType: 'eMBB',
        networkSlice: 'default',
        qosRequirements: {}
      });

      expect(connectionResult.connectionStatus.state).toBe('connected');

      // 第二步：创建网络切片
      const slicingResult = await slicingNode.execute({
        sliceRequirements: {
          bandwidth: 100,
          maxLatency: 20
        },
        networkResources: {
          availableBandwidth: 1000,
          minLatency: 1
        },
        sliceType: 'eMBB',
        tenantInfo: { id: 'tenant1' }
      });

      expect(slicingResult.sliceId).toBeDefined();
      expect(slicingResult.sliceConfiguration.sliceType).toBe('eMBB');
    });

    it('应该能够处理设备不兼容的情况', async () => {
      const connectionNode = new FiveGConnectionNode();

      await expect(connectionNode.execute({
        deviceInfo: {
          supportedNetworkTypes: ['4G'] // 不支持5G
        },
        connectionType: 'eMBB',
        networkSlice: 'default',
        qosRequirements: {}
      })).rejects.toThrow('设备不支持5G网络');
    });
  });

  describe('跨类别节点集成', () => {
    it('应该能够组合边缘路由、云边协调和5G网络节点', async () => {
      const routingNode = new EdgeRoutingNode();
      const orchestrationNode = new CloudEdgeOrchestrationNode();
      const connectionNode = new FiveGConnectionNode();

      // 模拟完整的边缘计算工作流
      
      // 1. 建立5G连接
      const connectionResult = await connectionNode.execute({
        deviceInfo: {
          supportedNetworkTypes: ['5G'],
          fiveGCapabilities: { eMBB: true }
        },
        connectionType: 'eMBB',
        networkSlice: 'default',
        qosRequirements: { minBandwidth: 100 }
      });

      expect(connectionResult.connectionStatus.state).toBe('connected');

      // 2. 边缘路由决策
      const routingResult = await routingNode.execute({
        clientInfo: { location: { latitude: 39.9042, longitude: 116.4074 } },
        routingPolicy: 'latency',
        edgeNodes: [
          { nodeId: 'edge1', status: 'active', latency: 5 }
        ],
        networkMetrics: {}
      });

      expect(routingResult.selectedNode).toBeDefined();

      // 3. 云边协调
      const orchestrationResult = await orchestrationNode.execute({
        cloudResources: [
          { id: 'cloud1', type: 'cloud', cpuCores: 16 }
        ],
        edgeNodes: [
          { id: routingResult.selectedNode.nodeId, type: 'edge', cpuCores: 4 }
        ],
        workloads: [
          { id: 'workload1', cpuRequirement: 2 }
        ],
        orchestrationPolicy: {}
      });

      expect(orchestrationResult.orchestrationPlan).toBeDefined();
    });

    it('应该能够处理复杂的错误传播', async () => {
      const routingNode = new EdgeRoutingNode();
      const orchestrationNode = new CloudEdgeOrchestrationNode();

      // 路由失败应该影响后续的协调
      try {
        await routingNode.execute({
          clientInfo: {},
          routingPolicy: 'latency',
          edgeNodes: [], // 没有可用节点
          networkMetrics: {}
        });
      } catch (routingError) {
        expect(routingError.message).toContain('没有可用的边缘节点');

        // 协调节点应该能够处理没有边缘节点的情况
        const orchestrationResult = await orchestrationNode.execute({
          cloudResources: [
            { id: 'cloud1', type: 'cloud', cpuCores: 16 }
          ],
          edgeNodes: [], // 没有边缘节点
          workloads: [
            { id: 'workload1', cpuRequirement: 2 }
          ],
          orchestrationPolicy: {}
        });

        // 应该仍然能够生成协调计划，只是所有工作负载都分配到云端
        expect(orchestrationResult.orchestrationPlan).toBeDefined();
      }
    });
  });

  describe('性能和可扩展性测试', () => {
    it('应该能够处理大量并发节点执行', async () => {
      const routingNode = new EdgeRoutingNode();
      const promises = [];

      // 创建100个并发路由请求
      for (let i = 0; i < 100; i++) {
        promises.push(
          routingNode.execute({
            clientInfo: { location: { latitude: 39.9042, longitude: 116.4074 } },
            routingPolicy: 'latency',
            edgeNodes: [
              { nodeId: `edge${i}`, status: 'active', latency: Math.random() * 50 }
            ],
            networkMetrics: {}
          })
        );
      }

      const results = await Promise.all(promises);
      expect(results).toHaveLength(100);
      results.forEach(result => {
        expect(result.selectedNode).toBeDefined();
      });
    });

    it('应该能够处理大规模数据输入', async () => {
      const orchestrationNode = new CloudEdgeOrchestrationNode();

      // 创建大量资源和工作负载
      const cloudResources = Array.from({ length: 50 }, (_, i) => ({
        id: `cloud${i}`,
        type: 'cloud',
        cpuCores: 16,
        memory: 32
      }));

      const edgeNodes = Array.from({ length: 100 }, (_, i) => ({
        id: `edge${i}`,
        type: 'edge',
        cpuCores: 4,
        memory: 8
      }));

      const workloads = Array.from({ length: 200 }, (_, i) => ({
        id: `workload${i}`,
        cpuRequirement: Math.floor(Math.random() * 4) + 1,
        memoryRequirement: Math.floor(Math.random() * 8) + 1
      }));

      const result = await orchestrationNode.execute({
        cloudResources,
        edgeNodes,
        workloads,
        orchestrationPolicy: {}
      });

      expect(result.orchestrationPlan).toBeDefined();
      expect(result.resourceAllocation).toBeDefined();
      expect(result.orchestrationMetrics.workloadAllocation).toBeGreaterThan(0);
    });
  });
});
