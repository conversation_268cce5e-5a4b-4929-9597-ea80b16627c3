/**
 * 边缘路由节点测试
 * 测试EdgeRoutingNode、EdgeLoadBalancingNode、EdgeCachingNode、EdgeCompressionNode
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from '../../../../src/visual-script/nodes/edge/EdgeRoutingNodes';

describe('EdgeRoutingNodes', () => {
  describe('EdgeRoutingNode', () => {
    let node: EdgeRoutingNode;

    beforeEach(() => {
      node = new EdgeRoutingNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('EdgeRoutingNode');
      expect(node.name).toBe('边缘路由');
      expect(node.category).toBe('边缘计算');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行路由决策', async () => {
      const inputs = {
        clientInfo: { location: { latitude: 39.9042, longitude: 116.4074 } },
        routingPolicy: 'latency',
        edgeNodes: [
          { nodeId: 'node1', status: 'active', latency: 10, load: 30 },
          { nodeId: 'node2', status: 'active', latency: 20, load: 50 }
        ],
        networkMetrics: {
          node1: { latency: 10 },
          node2: { latency: 20 }
        }
      };

      const result = await node.execute(inputs);

      expect(result.selectedNode).toBeDefined();
      expect(result.routingDecision).toBeDefined();
      expect(result.routingMetrics).toBeDefined();
      expect(result.selectedNode.nodeId).toBe('node1'); // 应该选择延迟更低的节点
    });

    it('应该处理没有可用节点的情况', async () => {
      const inputs = {
        clientInfo: {},
        routingPolicy: 'latency',
        edgeNodes: [
          { nodeId: 'node1', status: 'inactive' }
        ],
        networkMetrics: {}
      };

      await expect(node.execute(inputs)).rejects.toThrow('没有可用的边缘节点');
    });

    it('应该支持不同的路由策略', async () => {
      const inputs = {
        clientInfo: { location: { latitude: 39.9042, longitude: 116.4074 } },
        edgeNodes: [
          { nodeId: 'node1', status: 'active', load: 30, cost: 10 },
          { nodeId: 'node2', status: 'active', load: 50, cost: 5 }
        ],
        networkMetrics: {}
      };

      // 测试负载策略
      const loadResult = await node.execute({ ...inputs, routingPolicy: 'load' });
      expect(loadResult.selectedNode.nodeId).toBe('node1');

      // 测试成本策略
      const costResult = await node.execute({ ...inputs, routingPolicy: 'cost' });
      expect(costResult.selectedNode.nodeId).toBe('node2');
    });
  });

  describe('EdgeLoadBalancingNode', () => {
    let node: EdgeLoadBalancingNode;

    beforeEach(() => {
      node = new EdgeLoadBalancingNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('EdgeLoadBalancingNode');
      expect(node.name).toBe('边缘负载均衡');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行负载均衡', async () => {
      const inputs = {
        edgeNodes: [
          { nodeId: 'node1', activeConnections: 10, cpuUsage: 30 },
          { nodeId: 'node2', activeConnections: 20, cpuUsage: 50 }
        ],
        balancingAlgorithm: 'least_connections',
        requestInfo: {},
        healthChecks: {
          node1: { status: 'healthy' },
          node2: { status: 'healthy' }
        }
      };

      const result = await node.execute(inputs);

      expect(result.targetNode).toBeDefined();
      expect(result.loadDistribution).toBeDefined();
      expect(result.balancingMetrics).toBeDefined();
      expect(result.targetNode.nodeId).toBe('node1'); // 应该选择连接数更少的节点
    });

    it('应该过滤不健康的节点', async () => {
      const inputs = {
        edgeNodes: [
          { nodeId: 'node1', activeConnections: 10 },
          { nodeId: 'node2', activeConnections: 5 }
        ],
        balancingAlgorithm: 'least_connections',
        requestInfo: {},
        healthChecks: {
          node1: { status: 'unhealthy' },
          node2: { status: 'healthy' }
        }
      };

      const result = await node.execute(inputs);
      expect(result.targetNode.nodeId).toBe('node2');
    });

    it('应该处理没有健康节点的情况', async () => {
      const inputs = {
        edgeNodes: [
          { nodeId: 'node1', activeConnections: 10 }
        ],
        balancingAlgorithm: 'least_connections',
        requestInfo: {},
        healthChecks: {
          node1: { status: 'unhealthy' }
        }
      };

      await expect(node.execute(inputs)).rejects.toThrow('没有健康的边缘节点可用');
    });
  });

  describe('EdgeCachingNode', () => {
    let node: EdgeCachingNode;

    beforeEach(() => {
      node = new EdgeCachingNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('EdgeCachingNode');
      expect(node.name).toBe('边缘缓存');
      expect(node.inputs).toHaveLength(5);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行缓存操作', async () => {
      // 测试设置缓存
      const setResult = await node.execute({
        cacheKey: 'test-key',
        cacheValue: 'test-value',
        operation: 'set',
        ttl: 3600
      });

      expect(setResult.result).toBe(true);
      expect(setResult.cacheHit).toBe(false);
      expect(setResult.cacheStats).toBeDefined();

      // 测试获取缓存
      const getResult = await node.execute({
        cacheKey: 'test-key',
        operation: 'get'
      });

      expect(getResult.result).toBe('test-value');
      expect(getResult.cacheHit).toBe(true);
    });

    it('应该正确处理缓存过期', async () => {
      // 设置短TTL的缓存
      await node.execute({
        cacheKey: 'expire-key',
        cacheValue: 'expire-value',
        operation: 'set',
        ttl: 0.001 // 1毫秒
      });

      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 10));

      // 尝试获取过期的缓存
      const result = await node.execute({
        cacheKey: 'expire-key',
        operation: 'get'
      });

      expect(result.result).toBe(null);
      expect(result.cacheHit).toBe(false);
    });

    it('应该支持不同的缓存操作', async () => {
      // 设置缓存
      await node.execute({
        cacheKey: 'multi-op-key',
        cacheValue: 'multi-op-value',
        operation: 'set'
      });

      // 检查存在
      const existsResult = await node.execute({
        cacheKey: 'multi-op-key',
        operation: 'exists'
      });
      expect(existsResult.result).toBe(true);

      // 删除缓存
      const deleteResult = await node.execute({
        cacheKey: 'multi-op-key',
        operation: 'delete'
      });
      expect(deleteResult.result).toBe(true);

      // 再次检查存在
      const existsAfterDelete = await node.execute({
        cacheKey: 'multi-op-key',
        operation: 'exists'
      });
      expect(existsAfterDelete.result).toBe(false);
    });
  });

  describe('EdgeCompressionNode', () => {
    let node: EdgeCompressionNode;

    beforeEach(() => {
      node = new EdgeCompressionNode();
    });

    it('应该正确初始化节点', () => {
      expect(node.type).toBe('EdgeCompressionNode');
      expect(node.name).toBe('边缘压缩');
      expect(node.inputs).toHaveLength(4);
      expect(node.outputs).toHaveLength(3);
    });

    it('应该正确执行数据压缩', async () => {
      const testData = 'This is a test string for compression';
      
      const compressResult = await node.execute({
        data: testData,
        operation: 'compress',
        compressionType: 'gzip',
        compressionLevel: 6
      });

      expect(compressResult.result).toBeDefined();
      expect(compressResult.compressionRatio).toBeLessThan(1);
      expect(compressResult.processingTime).toBeGreaterThan(0);
    });

    it('应该正确执行数据解压缩', async () => {
      const testData = 'This is a test string for compression and decompression';
      
      // 先压缩
      const compressResult = await node.execute({
        data: testData,
        operation: 'compress',
        compressionType: 'gzip'
      });

      // 再解压缩
      const decompressResult = await node.execute({
        data: compressResult.result,
        operation: 'decompress',
        compressionType: 'gzip'
      });

      expect(decompressResult.result).toBe(testData);
    });

    it('应该支持不同的压缩类型', async () => {
      const testData = 'Test data for different compression types';
      const compressionTypes = ['gzip', 'deflate', 'brotli', 'lz4'];

      for (const type of compressionTypes) {
        const result = await node.execute({
          data: testData,
          operation: 'compress',
          compressionType: type
        });

        expect(result.result).toBeDefined();
        expect(result.compressionRatio).toBeGreaterThan(0);
      }
    });

    it('应该处理无效的压缩类型', async () => {
      await expect(node.execute({
        data: 'test',
        operation: 'compress',
        compressionType: 'invalid'
      })).rejects.toThrow('不支持的压缩类型');
    });

    it('应该处理无效的操作类型', async () => {
      await expect(node.execute({
        data: 'test',
        operation: 'invalid'
      })).rejects.toThrow('不支持的压缩操作');
    });
  });
});
