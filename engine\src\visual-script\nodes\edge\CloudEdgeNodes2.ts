/**
 * 云边协调节点 - 第二部分
 * 实现数据同步、任务分发、资源优化等功能
 */

import { BaseNode } from '../BaseNode';
import { NodeInput, NodeOutput } from '../../types/NodeTypes';

/**
 * 数据同步节点
 * 提供云端和边缘之间的数据同步功能
 */
export class DataSynchronizationNode extends BaseNode {
  constructor() {
    super('DataSynchronizationNode', '数据同步', '边缘计算');
    
    this.inputs = [
      { name: 'sourceData', type: 'any', label: '源数据' },
      { name: 'syncTargets', type: 'array', label: '同步目标' },
      { name: 'syncStrategy', type: 'string', label: '同步策略' },
      { name: 'conflictResolution', type: 'string', label: '冲突解决策略' }
    ];
    
    this.outputs = [
      { name: 'syncResult', type: 'object', label: '同步结果' },
      { name: 'syncStatus', type: 'object', label: '同步状态' },
      { name: 'conflicts', type: 'array', label: '同步冲突' }
    ];
  }

  async execute(inputs: Record<string, any>): Promise<Record<string, any>> {
    const { 
      sourceData = {}, 
      syncTargets = [],
      syncStrategy = 'incremental',
      conflictResolution = 'last_write_wins'
    } = inputs;

    try {
      const syncResult = await this.performDataSync(
        sourceData,
        syncTargets,
        syncStrategy,
        conflictResolution
      );

      return {
        syncResult: syncResult.result,
        syncStatus: syncResult.status,
        conflicts: syncResult.conflicts
      };
    } catch (error) {
      throw new Error(`数据同步失败: ${error.message}`);
    }
  }

  private async performDataSync(
    sourceData: any,
    targets: any[],
    strategy: string,
    conflictResolution: string
  ): Promise<any> {
    const syncResults = [];
    const conflicts = [];
    let overallStatus = 'success';

    for (const target of targets) {
      try {
        const result = await this.syncToTarget(sourceData, target, strategy, conflictResolution);
        syncResults.push(result);
        
        if (result.conflicts && result.conflicts.length > 0) {
          conflicts.push(...result.conflicts);
        }
        
        if (result.status !== 'success') {
          overallStatus = 'partial';
        }
      } catch (error) {
        syncResults.push({
          targetId: target.id,
          status: 'failed',
          error: error.message
        });
        overallStatus = 'failed';
      }
    }

    return {
      result: syncResults,
      status: {
        overall: overallStatus,
        successCount: syncResults.filter(r => r.status === 'success').length,
        failedCount: syncResults.filter(r => r.status === 'failed').length,
        totalTargets: targets.length,
        timestamp: Date.now()
      },
      conflicts
    };
  }

  private async syncToTarget(
    sourceData: any,
    target: any,
    strategy: string,
    conflictResolution: string
  ): Promise<any> {
    // 获取目标数据
    const targetData = await this.getTargetData(target);
    
    // 比较数据差异
    const diff = this.compareData(sourceData, targetData);
    
    // 处理冲突
    const resolvedData = this.resolveConflicts(diff, conflictResolution);
    
    // 执行同步
    const syncResult = await this.executeSyncStrategy(resolvedData, target, strategy);

    return {
      targetId: target.id,
      status: syncResult.success ? 'success' : 'failed',
      syncedItems: syncResult.syncedItems,
      conflicts: resolvedData.conflicts,
      timestamp: Date.now()
    };
  }

  private async getTargetData(target: any): Promise<any> {
    // 模拟获取目标数据
    return target.currentData || {};
  }

  private compareData(sourceData: any, targetData: any): any {
    const diff = {
      added: [],
      modified: [],
      deleted: [],
      conflicts: []
    };

    // 比较源数据和目标数据
    for (const key in sourceData) {
      if (!(key in targetData)) {
        diff.added.push({ key, value: sourceData[key] });
      } else if (JSON.stringify(sourceData[key]) !== JSON.stringify(targetData[key])) {
        diff.modified.push({
          key,
          sourceValue: sourceData[key],
          targetValue: targetData[key]
        });
      }
    }

    // 检查删除的项
    for (const key in targetData) {
      if (!(key in sourceData)) {
        diff.deleted.push({ key, value: targetData[key] });
      }
    }

    return diff;
  }

  private resolveConflicts(diff: any, strategy: string): any {
    const resolved = { ...diff, conflicts: [] };

    diff.modified.forEach(item => {
      switch (strategy) {
        case 'last_write_wins':
          // 源数据优先
          break;
        case 'first_write_wins':
          // 目标数据优先，从修改列表中移除
          resolved.modified = resolved.modified.filter(m => m.key !== item.key);
          break;
        case 'manual_resolution':
          // 标记为冲突，需要手动解决
          resolved.conflicts.push({
            type: 'data_conflict',
            key: item.key,
            sourceValue: item.sourceValue,
            targetValue: item.targetValue,
            resolution: 'manual'
          });
          resolved.modified = resolved.modified.filter(m => m.key !== item.key);
          break;
        case 'merge':
          // 尝试合并数据
          const merged = this.mergeValues(item.sourceValue, item.targetValue);
          if (merged.success) {
            item.sourceValue = merged.value;
          } else {
            resolved.conflicts.push({
              type: 'merge_conflict',
              key: item.key,
              sourceValue: item.sourceValue,
              targetValue: item.targetValue,
              resolution: 'failed'
            });
          }
          break;
      }
    });

    return resolved;
  }

  private mergeValues(sourceValue: any, targetValue: any): any {
    // 简单的合并逻辑
    if (typeof sourceValue === 'object' && typeof targetValue === 'object') {
      try {
        const merged = { ...targetValue, ...sourceValue };
        return { success: true, value: merged };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }
    
    // 对于非对象类型，无法自动合并
    return { success: false, error: 'Cannot merge non-object values' };
  }

  private async executeSyncStrategy(resolvedData: any, target: any, strategy: string): Promise<any> {
    let syncedItems = 0;
    
    try {
      switch (strategy) {
        case 'full':
          syncedItems = await this.performFullSync(resolvedData, target);
          break;
        case 'incremental':
          syncedItems = await this.performIncrementalSync(resolvedData, target);
          break;
        case 'delta':
          syncedItems = await this.performDeltaSync(resolvedData, target);
          break;
        default:
          throw new Error(`不支持的同步策略: ${strategy}`);
      }

      return { success: true, syncedItems };
    } catch (error) {
      return { success: false, error: error.message, syncedItems };
    }
  }

  private async performFullSync(data: any, target: any): Promise<number> {
    // 全量同步：替换所有数据
    let count = 0;
    count += data.added.length;
    count += data.modified.length;
    count += data.deleted.length;
    
    // 模拟同步操作
    await this.simulateNetworkDelay(count * 10);
    
    return count;
  }

  private async performIncrementalSync(data: any, target: any): Promise<number> {
    // 增量同步：只同步变更
    let count = 0;
    
    // 添加新项
    for (const item of data.added) {
      await this.simulateNetworkDelay(5);
      count++;
    }
    
    // 修改现有项
    for (const item of data.modified) {
      await this.simulateNetworkDelay(8);
      count++;
    }
    
    // 删除项（可选）
    if (target.allowDeletion) {
      for (const item of data.deleted) {
        await this.simulateNetworkDelay(3);
        count++;
      }
    }
    
    return count;
  }

  private async performDeltaSync(data: any, target: any): Promise<number> {
    // 差量同步：只传输差异部分
    let count = 0;
    
    // 计算差异大小
    const deltaSize = this.calculateDeltaSize(data);
    
    if (deltaSize > 0) {
      await this.simulateNetworkDelay(deltaSize);
      count = data.added.length + data.modified.length;
    }
    
    return count;
  }

  private calculateDeltaSize(data: any): number {
    // 简化的差异大小计算
    return data.added.length * 10 + data.modified.length * 15 + data.deleted.length * 5;
  }

  private async simulateNetworkDelay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 任务分发节点
 * 提供任务在云端和边缘之间的智能分发功能
 */
export class TaskDistributionNode extends BaseNode {
  constructor() {
    super('TaskDistributionNode', '任务分发', '边缘计算');
    
    this.inputs = [
      { name: 'tasks', type: 'array', label: '任务列表' },
      { name: 'availableNodes', type: 'array', label: '可用节点' },
      { name: 'distributionPolicy', type: 'object', label: '分发策略' },
      { name: 'constraints', type: 'object', label: '约束条件' }
    ];
    
    this.outputs = [
      { name: 'distributionPlan', type: 'object', label: '分发计划' },
      { name: 'taskAssignments', type: 'array', label: '任务分配' },
      { name: 'distributionMetrics', type: 'object', label: '分发指标' }
    ];
  }

  async execute(inputs: Record<string, any>): Promise<Record<string, any>> {
    const { 
      tasks = [], 
      availableNodes = [],
      distributionPolicy = {},
      constraints = {}
    } = inputs;

    try {
      const distribution = await this.performTaskDistribution(
        tasks,
        availableNodes,
        distributionPolicy,
        constraints
      );

      return {
        distributionPlan: distribution.plan,
        taskAssignments: distribution.assignments,
        distributionMetrics: distribution.metrics
      };
    } catch (error) {
      throw new Error(`任务分发失败: ${error.message}`);
    }
  }

  private async performTaskDistribution(
    tasks: any[],
    nodes: any[],
    policy: any,
    constraints: any
  ): Promise<any> {
    // 任务优先级排序
    const sortedTasks = this.sortTasksByPriority(tasks, policy);
    
    // 节点能力评估
    const nodeCapabilities = this.assessNodeCapabilities(nodes);
    
    // 任务分配
    const assignments = this.assignTasks(sortedTasks, nodeCapabilities, policy, constraints);
    
    // 生成分发计划
    const plan = this.generateDistributionPlan(assignments, policy);
    
    // 计算分发指标
    const metrics = this.calculateDistributionMetrics(assignments, tasks, nodes);

    return { plan, assignments, metrics };
  }

  private sortTasksByPriority(tasks: any[], policy: any): any[] {
    const priorityWeight = policy.priorityWeight || 1.0;
    const deadlineWeight = policy.deadlineWeight || 1.0;
    const resourceWeight = policy.resourceWeight || 0.5;

    return tasks.sort((a, b) => {
      const aScore = (a.priority || 0) * priorityWeight + 
                    (a.deadline ? (Date.now() - a.deadline) : 0) * deadlineWeight +
                    (a.resourceRequirement || 0) * resourceWeight;
      const bScore = (b.priority || 0) * priorityWeight + 
                    (b.deadline ? (Date.now() - b.deadline) : 0) * deadlineWeight +
                    (b.resourceRequirement || 0) * resourceWeight;
      return bScore - aScore;
    });
  }

  private assessNodeCapabilities(nodes: any[]): any[] {
    return nodes.map(node => ({
      ...node,
      capability: this.calculateNodeCapability(node),
      availability: this.calculateNodeAvailability(node),
      efficiency: this.calculateNodeEfficiency(node)
    }));
  }

  private calculateNodeCapability(node: any): number {
    const cpuScore = (node.cpuCores || 0) * 0.4;
    const memoryScore = (node.memory || 0) / 1000 * 0.3; // GB转换
    const storageScore = (node.storage || 0) / 10000 * 0.2; // GB转换
    const networkScore = (node.bandwidth || 0) / 1000 * 0.1; // Mbps转换
    
    return cpuScore + memoryScore + storageScore + networkScore;
  }

  private calculateNodeAvailability(node: any): number {
    const cpuAvail = Math.max(0, 100 - (node.cpuUsage || 0)) / 100;
    const memoryAvail = Math.max(0, 100 - (node.memoryUsage || 0)) / 100;
    const storageAvail = Math.max(0, 100 - (node.storageUsage || 0)) / 100;
    
    return (cpuAvail + memoryAvail + storageAvail) / 3;
  }

  private calculateNodeEfficiency(node: any): number {
    const latencyScore = Math.max(0, 1 - (node.latency || 0) / 1000);
    const reliabilityScore = (node.reliability || 95) / 100;
    const costScore = Math.max(0, 1 - (node.cost || 0) / 10);
    
    return (latencyScore + reliabilityScore + costScore) / 3;
  }

  private assignTasks(tasks: any[], nodes: any[], policy: any, constraints: any): any[] {
    const assignments = [];
    const nodeLoads = new Map();
    
    // 初始化节点负载
    nodes.forEach(node => {
      nodeLoads.set(node.id, {
        cpuLoad: node.cpuUsage || 0,
        memoryLoad: node.memoryUsage || 0,
        taskCount: 0
      });
    });

    tasks.forEach(task => {
      const suitableNode = this.findSuitableNode(task, nodes, nodeLoads, policy, constraints);
      
      if (suitableNode) {
        assignments.push({
          taskId: task.id,
          nodeId: suitableNode.id,
          nodeType: suitableNode.type,
          assignmentScore: this.calculateAssignmentScore(task, suitableNode),
          estimatedStartTime: this.estimateStartTime(suitableNode, nodeLoads),
          estimatedCompletionTime: this.estimateCompletionTime(task, suitableNode)
        });
        
        // 更新节点负载
        const load = nodeLoads.get(suitableNode.id);
        load.cpuLoad += task.cpuRequirement || 0;
        load.memoryLoad += task.memoryRequirement || 0;
        load.taskCount += 1;
      } else {
        assignments.push({
          taskId: task.id,
          nodeId: null,
          status: 'unassigned',
          reason: 'No suitable node found'
        });
      }
    });

    return assignments;
  }

  private findSuitableNode(task: any, nodes: any[], nodeLoads: Map<string, any>, policy: any, constraints: any): any {
    const candidateNodes = nodes.filter(node => {
      return this.meetsTaskRequirements(task, node, nodeLoads.get(node.id)) &&
             this.meetsConstraints(task, node, constraints);
    });

    if (candidateNodes.length === 0) {
      return null;
    }

    // 根据策略选择最佳节点
    return this.selectBestNode(task, candidateNodes, policy);
  }

  private meetsTaskRequirements(task: any, node: any, load: any): boolean {
    const cpuOk = (load.cpuLoad + (task.cpuRequirement || 0)) <= 100;
    const memoryOk = (load.memoryLoad + (task.memoryRequirement || 0)) <= 100;
    const latencyOk = !task.maxLatency || (node.latency || 0) <= task.maxLatency;
    
    return cpuOk && memoryOk && latencyOk;
  }

  private meetsConstraints(task: any, node: any, constraints: any): boolean {
    // 地理约束
    if (constraints.allowedRegions && !constraints.allowedRegions.includes(node.region)) {
      return false;
    }
    
    // 节点类型约束
    if (task.requiredNodeType && task.requiredNodeType !== node.type) {
      return false;
    }
    
    // 安全级别约束
    if (task.securityLevel && (node.securityLevel || 0) < task.securityLevel) {
      return false;
    }
    
    return true;
  }

  private selectBestNode(task: any, nodes: any[], policy: any): any {
    const strategy = policy.selectionStrategy || 'balanced';
    
    switch (strategy) {
      case 'performance':
        return nodes.reduce((best, current) => 
          current.capability > best.capability ? current : best);
      case 'cost':
        return nodes.reduce((best, current) => 
          (current.cost || 0) < (best.cost || 0) ? current : best);
      case 'latency':
        return nodes.reduce((best, current) => 
          (current.latency || 0) < (best.latency || 0) ? current : best);
      case 'balanced':
      default:
        return nodes.reduce((best, current) => {
          const currentScore = this.calculateAssignmentScore(task, current);
          const bestScore = this.calculateAssignmentScore(task, best);
          return currentScore > bestScore ? current : best;
        });
    }
  }

  private calculateAssignmentScore(task: any, node: any): number {
    let score = 0;
    
    // 性能匹配分数
    const performanceMatch = Math.min(1, node.capability / (task.resourceRequirement || 1));
    score += performanceMatch * 0.3;
    
    // 可用性分数
    score += node.availability * 0.3;
    
    // 效率分数
    score += node.efficiency * 0.2;
    
    // 负载均衡分数
    const loadBalance = Math.max(0, 1 - (node.cpuUsage || 0) / 100);
    score += loadBalance * 0.2;
    
    return score;
  }

  private estimateStartTime(node: any, nodeLoads: Map<string, any>): number {
    const load = nodeLoads.get(node.id);
    const queueDelay = load.taskCount * 30; // 每个任务30秒延迟
    return Date.now() + queueDelay * 1000;
  }

  private estimateCompletionTime(task: any, node: any): number {
    const processingTime = (task.estimatedTime || 60) * 1000; // 默认60秒
    const performanceFactor = node.capability || 1;
    const adjustedTime = processingTime / performanceFactor;
    
    return this.estimateStartTime(node, new Map()) + adjustedTime;
  }

  private generateDistributionPlan(assignments: any[], policy: any): any {
    const assignedTasks = assignments.filter(a => a.nodeId);
    const unassignedTasks = assignments.filter(a => !a.nodeId);
    
    return {
      totalTasks: assignments.length,
      assignedTasks: assignedTasks.length,
      unassignedTasks: unassignedTasks.length,
      distributionStrategy: policy.selectionStrategy || 'balanced',
      executionPhases: this.generateExecutionPhases(assignedTasks),
      contingencyPlan: this.generateContingencyPlan(unassignedTasks)
    };
  }

  private generateExecutionPhases(assignments: any[]): any[] {
    // 按预计开始时间分组
    const phases = [];
    const timeSlots = new Map();
    
    assignments.forEach(assignment => {
      const timeSlot = Math.floor(assignment.estimatedStartTime / (5 * 60 * 1000)); // 5分钟时间槽
      if (!timeSlots.has(timeSlot)) {
        timeSlots.set(timeSlot, []);
      }
      timeSlots.get(timeSlot).push(assignment);
    });
    
    Array.from(timeSlots.entries()).sort((a, b) => a[0] - b[0]).forEach(([slot, tasks], index) => {
      phases.push({
        phase: index + 1,
        startTime: slot * 5 * 60 * 1000,
        tasks: tasks.length,
        taskIds: tasks.map(t => t.taskId)
      });
    });
    
    return phases;
  }

  private generateContingencyPlan(unassignedTasks: any[]): any {
    return {
      unassignedCount: unassignedTasks.length,
      actions: [
        'Scale up available resources',
        'Relax task constraints',
        'Queue tasks for later execution',
        'Notify administrators'
      ],
      retryStrategy: {
        maxRetries: 3,
        retryInterval: 300000, // 5分钟
        backoffMultiplier: 2
      }
    };
  }

  private calculateDistributionMetrics(assignments: any[], tasks: any[], nodes: any[]): any {
    const assigned = assignments.filter(a => a.nodeId);
    const unassigned = assignments.filter(a => !a.nodeId);
    
    const cloudAssignments = assigned.filter(a => a.nodeType === 'cloud');
    const edgeAssignments = assigned.filter(a => a.nodeType === 'edge');
    
    const avgAssignmentScore = assigned.length > 0 ? 
      assigned.reduce((sum, a) => sum + (a.assignmentScore || 0), 0) / assigned.length : 0;

    return {
      assignmentRate: (assigned.length / tasks.length) * 100,
      cloudEdgeRatio: cloudAssignments.length > 0 ? 
        edgeAssignments.length / cloudAssignments.length : edgeAssignments.length,
      averageAssignmentScore: avgAssignmentScore.toFixed(3),
      nodeUtilization: this.calculateNodeUtilization(assigned, nodes),
      distributionEfficiency: this.calculateDistributionEfficiency(assigned),
      unassignedTasks: unassigned.length,
      timestamp: Date.now()
    };
  }

  private calculateNodeUtilization(assignments: any[], nodes: any[]): any {
    const utilization = {};
    
    nodes.forEach(node => {
      const nodeAssignments = assignments.filter(a => a.nodeId === node.id);
      utilization[node.id] = {
        taskCount: nodeAssignments.length,
        utilizationRate: nodeAssignments.length > 0 ? 
          (nodeAssignments.length / assignments.length) * 100 : 0
      };
    });
    
    return utilization;
  }

  private calculateDistributionEfficiency(assignments: any[]): number {
    if (assignments.length === 0) return 0;
    
    const totalScore = assignments.reduce((sum, a) => sum + (a.assignmentScore || 0), 0);
    const maxPossibleScore = assignments.length * 1.0; // 假设最高分为1.0
    
    return (totalScore / maxPossibleScore) * 100;
  }
}
