/**
 * 机器学习节点 - 第三部分
 * 完成批次3.3的机器学习节点（5-10）
 */

import { VisualScriptNode } from '../../VisualScriptNode';
import { MachineLearningNode } from './MachineLearningNodes';

/**
 * 5. 超参数调优节点
 */
export class HyperparameterTuningNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/hyperparameterTuning';
  public static readonly NAME = '超参数调优';
  public static readonly DESCRIPTION = '自动超参数优化';

  private searchHistory: any[] = [];
  private bestParams: any = null;
  private bestScore: number = -Infinity;

  constructor() {
    super(HyperparameterTuningNode.TYPE, HyperparameterTuningNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('parameterSpace', 'object', '参数空间', {});
    this.addInput('objectiveFunction', 'string', '目标函数', 'accuracy');
    this.addInput('searchMethod', 'string', '搜索方法', 'random_search');
    this.addInput('maxIterations', 'number', '最大迭代次数', 50);
    this.addInput('validationData', 'array', '验证数据', []);
  }

  private setupOutputs(): void {
    this.addOutput('bestParameters', 'object', '最佳参数');
    this.addOutput('bestScore', 'number', '最佳得分');
    this.addOutput('searchHistory', 'array', '搜索历史');
    this.addOutput('convergenceInfo', 'object', '收敛信息');
  }

  public execute(inputs: any): any {
    try {
      const parameterSpace = this.getInputValue(inputs, 'parameterSpace');
      const objectiveFunction = this.getInputValue(inputs, 'objectiveFunction');
      const searchMethod = this.getInputValue(inputs, 'searchMethod');
      const maxIterations = this.getInputValue(inputs, 'maxIterations');
      const validationData = this.getInputValue(inputs, 'validationData');

      if (!parameterSpace || Object.keys(parameterSpace).length === 0) {
        throw new Error('参数空间无效');
      }

      // 执行超参数搜索
      const searchResult = this.performHyperparameterSearch(
        parameterSpace, objectiveFunction, searchMethod, maxIterations, validationData
      );

      return {
        bestParameters: searchResult.bestParams,
        bestScore: searchResult.bestScore,
        searchHistory: searchResult.history,
        convergenceInfo: searchResult.convergence,
        result: { 
          status: 'completed', 
          searchMethod,
          iterations: searchResult.history.length,
          improvement: searchResult.bestScore - (this.searchHistory[0]?.score || 0)
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        bestParameters: {},
        bestScore: 0,
        searchHistory: [],
        convergenceInfo: {},
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '超参数调优失败'
      };
    }
  }

  private performHyperparameterSearch(
    paramSpace: any, objective: string, method: string, maxIter: number, validationData: any[]
  ): any {
    switch (method) {
      case 'random_search':
        return this.randomSearch(paramSpace, objective, maxIter, validationData);
      case 'grid_search':
        return this.gridSearch(paramSpace, objective, maxIter, validationData);
      case 'bayesian_optimization':
        return this.bayesianOptimization(paramSpace, objective, maxIter, validationData);
      default:
        return this.randomSearch(paramSpace, objective, maxIter, validationData);
    }
  }

  private randomSearch(paramSpace: any, objective: string, maxIter: number, validationData: any[]): any {
    const history: any[] = [];
    let bestParams = null;
    let bestScore = -Infinity;

    for (let i = 0; i < maxIter; i++) {
      // 随机采样参数
      const params = this.sampleRandomParameters(paramSpace);
      
      // 评估参数
      const score = this.evaluateParameters(params, objective, validationData);
      
      history.push({ iteration: i, parameters: params, score });
      
      if (score > bestScore) {
        bestScore = score;
        bestParams = { ...params };
      }
    }

    const convergence = this.analyzeConvergence(history);

    return { bestParams, bestScore, history, convergence };
  }

  private gridSearch(paramSpace: any, objective: string, maxIter: number, validationData: any[]): any {
    const paramNames = Object.keys(paramSpace);
    const paramGrids = paramNames.map(name => this.generateParameterGrid(paramSpace[name]));
    
    const history: any[] = [];
    let bestParams = null;
    let bestScore = -Infinity;
    let iteration = 0;

    // 生成所有参数组合
    const combinations = this.generateCombinations(paramGrids);
    
    for (const combination of combinations) {
      if (iteration >= maxIter) break;
      
      const params: any = {};
      paramNames.forEach((name, index) => {
        params[name] = combination[index];
      });
      
      const score = this.evaluateParameters(params, objective, validationData);
      
      history.push({ iteration, parameters: params, score });
      
      if (score > bestScore) {
        bestScore = score;
        bestParams = { ...params };
      }
      
      iteration++;
    }

    const convergence = this.analyzeConvergence(history);

    return { bestParams, bestScore, history, convergence };
  }

  private bayesianOptimization(paramSpace: any, objective: string, maxIter: number, validationData: any[]): any {
    const history: any[] = [];
    let bestParams = null;
    let bestScore = -Infinity;

    // 初始随机采样
    const initialSamples = Math.min(5, Math.floor(maxIter * 0.2));
    
    for (let i = 0; i < initialSamples; i++) {
      const params = this.sampleRandomParameters(paramSpace);
      const score = this.evaluateParameters(params, objective, validationData);
      
      history.push({ iteration: i, parameters: params, score });
      
      if (score > bestScore) {
        bestScore = score;
        bestParams = { ...params };
      }
    }

    // 贝叶斯优化迭代
    for (let i = initialSamples; i < maxIter; i++) {
      // 基于历史数据选择下一个参数点
      const params = this.selectNextParameters(paramSpace, history);
      const score = this.evaluateParameters(params, objective, validationData);
      
      history.push({ iteration: i, parameters: params, score });
      
      if (score > bestScore) {
        bestScore = score;
        bestParams = { ...params };
      }
    }

    const convergence = this.analyzeConvergence(history);

    return { bestParams, bestScore, history, convergence };
  }

  private sampleRandomParameters(paramSpace: any): any {
    const params: any = {};
    
    for (const [name, space] of Object.entries(paramSpace)) {
      params[name] = this.sampleFromSpace(space as any);
    }
    
    return params;
  }

  private sampleFromSpace(space: any): any {
    if (space.type === 'continuous') {
      return space.min + Math.random() * (space.max - space.min);
    } else if (space.type === 'discrete') {
      const index = Math.floor(Math.random() * space.values.length);
      return space.values[index];
    } else if (space.type === 'integer') {
      return Math.floor(space.min + Math.random() * (space.max - space.min + 1));
    } else if (Array.isArray(space)) {
      const index = Math.floor(Math.random() * space.length);
      return space[index];
    }
    return space;
  }

  private evaluateParameters(params: any, objective: string, validationData: any[]): number {
    // 模拟参数评估
    let score = 0.5; // 基础分数
    
    // 基于参数值计算得分
    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'number') {
        score += Math.sin(value) * 0.1;
      }
    }
    
    // 添加随机噪声
    score += (Math.random() - 0.5) * 0.2;
    
    // 确保分数在合理范围内
    return Math.max(0, Math.min(1, score));
  }

  private generateParameterGrid(space: any): any[] {
    if (space.type === 'continuous') {
      const steps = space.steps || 10;
      const stepSize = (space.max - space.min) / (steps - 1);
      return Array.from({ length: steps }, (_, i) => space.min + i * stepSize);
    } else if (space.type === 'discrete' || Array.isArray(space)) {
      return Array.isArray(space) ? space : space.values;
    } else if (space.type === 'integer') {
      const values = [];
      for (let i = space.min; i <= space.max; i++) {
        values.push(i);
      }
      return values;
    }
    return [space];
  }

  private generateCombinations(grids: any[][]): any[][] {
    if (grids.length === 0) return [[]];
    if (grids.length === 1) return grids[0].map(v => [v]);
    
    const result: any[][] = [];
    const firstGrid = grids[0];
    const restCombinations = this.generateCombinations(grids.slice(1));
    
    for (const value of firstGrid) {
      for (const combination of restCombinations) {
        result.push([value, ...combination]);
      }
    }
    
    return result;
  }

  private selectNextParameters(paramSpace: any, history: any[]): any {
    // 简化的贝叶斯优化：选择探索性参数
    const params: any = {};
    
    for (const [name, space] of Object.entries(paramSpace)) {
      // 基于历史数据的简单启发式选择
      const historicalValues = history.map(h => h.parameters[name]);
      const bestValue = history.reduce((best, h) => 
        h.score > best.score ? h : best
      ).parameters[name];
      
      // 在最佳值附近探索
      if (typeof bestValue === 'number') {
        const noise = (Math.random() - 0.5) * 0.1;
        params[name] = this.clampToSpace(bestValue + noise, space as any);
      } else {
        params[name] = this.sampleFromSpace(space as any);
      }
    }
    
    return params;
  }

  private clampToSpace(value: number, space: any): number {
    if (space.type === 'continuous') {
      return Math.max(space.min, Math.min(space.max, value));
    } else if (space.type === 'integer') {
      return Math.max(space.min, Math.min(space.max, Math.round(value)));
    }
    return value;
  }

  private analyzeConvergence(history: any[]): any {
    if (history.length < 2) {
      return { converged: false, plateau: false, improvement: 0 };
    }

    const scores = history.map(h => h.score);
    const bestScores = scores.map((_, i) => Math.max(...scores.slice(0, i + 1)));
    
    // 检查是否收敛
    const recentImprovement = bestScores[bestScores.length - 1] - bestScores[Math.max(0, bestScores.length - 10)];
    const converged = recentImprovement < 0.001;
    
    // 检查是否达到平台期
    const lastTenScores = scores.slice(-10);
    const plateau = lastTenScores.length >= 10 && 
      Math.max(...lastTenScores) - Math.min(...lastTenScores) < 0.01;

    return {
      converged,
      plateau,
      improvement: recentImprovement,
      bestScoreHistory: bestScores
    };
  }
}

/**
 * 6. 模型验证节点
 */
export class ModelValidationNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/modelValidation';
  public static readonly NAME = '模型验证';
  public static readonly DESCRIPTION = '模型性能验证和评估';

  constructor() {
    super(ModelValidationNode.TYPE, ModelValidationNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('model', 'object', '模型', {});
    this.addInput('testData', 'array', '测试数据', []);
    this.addInput('testLabels', 'array', '测试标签', []);
    this.addInput('validationMethod', 'string', '验证方法', 'holdout');
    this.addInput('metrics', 'array', '评估指标', ['accuracy', 'precision', 'recall']);
  }

  private setupOutputs(): void {
    this.addOutput('validationResults', 'object', '验证结果');
    this.addOutput('confusionMatrix', 'array', '混淆矩阵');
    this.addOutput('performanceMetrics', 'object', '性能指标');
    this.addOutput('validationReport', 'object', '验证报告');
  }

  public execute(inputs: any): any {
    try {
      const model = this.getInputValue(inputs, 'model');
      const testData = this.getInputValue(inputs, 'testData');
      const testLabels = this.getInputValue(inputs, 'testLabels');
      const validationMethod = this.getInputValue(inputs, 'validationMethod');
      const metrics = this.getInputValue(inputs, 'metrics');

      if (!model || !testData || !testLabels) {
        throw new Error('模型、测试数据或标签无效');
      }

      // 执行模型验证
      const validationResult = this.performValidation(
        model, testData, testLabels, validationMethod, metrics
      );

      return {
        validationResults: validationResult.results,
        confusionMatrix: validationResult.confusionMatrix,
        performanceMetrics: validationResult.metrics,
        validationReport: validationResult.report,
        result: { 
          status: 'validated', 
          method: validationMethod,
          sampleCount: testData.length,
          accuracy: validationResult.metrics.accuracy
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        validationResults: {},
        confusionMatrix: [],
        performanceMetrics: {},
        validationReport: {},
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '模型验证失败'
      };
    }
  }

  private performValidation(
    model: any, testData: any[], testLabels: any[], method: string, metrics: string[]
  ): any {
    // 获取模型预测
    const predictions = this.getPredictions(model, testData);
    
    // 计算混淆矩阵
    const confusionMatrix = this.computeConfusionMatrix(testLabels, predictions);
    
    // 计算性能指标
    const performanceMetrics = this.computeMetrics(testLabels, predictions, metrics, confusionMatrix);
    
    // 生成验证报告
    const report = this.generateValidationReport(
      method, testData.length, performanceMetrics, confusionMatrix
    );

    return {
      results: { predictions, actualLabels: testLabels },
      confusionMatrix,
      metrics: performanceMetrics,
      report
    };
  }

  private getPredictions(model: any, testData: any[]): number[] {
    // 模拟模型预测
    return testData.map((sample, index) => {
      // 基于样本特征的简单预测逻辑
      const sum = Array.isArray(sample) ? 
        sample.reduce((acc, val) => acc + (typeof val === 'number' ? val : 0), 0) : 
        Math.random();
      
      return sum > 0.5 ? 1 : 0;
    });
  }

  private computeConfusionMatrix(actual: number[], predicted: number[]): number[][] {
    const classes = [...new Set([...actual, ...predicted])].sort();
    const matrix: number[][] = Array(classes.length).fill(0).map(() => Array(classes.length).fill(0));
    
    for (let i = 0; i < actual.length; i++) {
      const actualIndex = classes.indexOf(actual[i]);
      const predictedIndex = classes.indexOf(predicted[i]);
      if (actualIndex >= 0 && predictedIndex >= 0) {
        matrix[actualIndex][predictedIndex]++;
      }
    }
    
    return matrix;
  }

  private computeMetrics(actual: number[], predicted: number[], metrics: string[], confusionMatrix: number[][]): any {
    const result: any = {};
    
    for (const metric of metrics) {
      switch (metric) {
        case 'accuracy':
          result.accuracy = this.computeAccuracy(actual, predicted);
          break;
        case 'precision':
          result.precision = this.computePrecision(confusionMatrix);
          break;
        case 'recall':
          result.recall = this.computeRecall(confusionMatrix);
          break;
        case 'f1_score':
          result.f1_score = this.computeF1Score(result.precision, result.recall);
          break;
        case 'specificity':
          result.specificity = this.computeSpecificity(confusionMatrix);
          break;
        case 'auc':
          result.auc = this.computeAUC(actual, predicted);
          break;
      }
    }
    
    return result;
  }

  private computeAccuracy(actual: number[], predicted: number[]): number {
    const correct = actual.filter((val, i) => val === predicted[i]).length;
    return correct / actual.length;
  }

  private computePrecision(confusionMatrix: number[][]): number {
    if (confusionMatrix.length !== 2) return 0; // 简化为二分类
    
    const tp = confusionMatrix[1][1];
    const fp = confusionMatrix[0][1];
    
    return tp + fp > 0 ? tp / (tp + fp) : 0;
  }

  private computeRecall(confusionMatrix: number[][]): number {
    if (confusionMatrix.length !== 2) return 0; // 简化为二分类
    
    const tp = confusionMatrix[1][1];
    const fn = confusionMatrix[1][0];
    
    return tp + fn > 0 ? tp / (tp + fn) : 0;
  }

  private computeF1Score(precision: number, recall: number): number {
    return precision + recall > 0 ? 2 * (precision * recall) / (precision + recall) : 0;
  }

  private computeSpecificity(confusionMatrix: number[][]): number {
    if (confusionMatrix.length !== 2) return 0; // 简化为二分类
    
    const tn = confusionMatrix[0][0];
    const fp = confusionMatrix[0][1];
    
    return tn + fp > 0 ? tn / (tn + fp) : 0;
  }

  private computeAUC(actual: number[], predicted: number[]): number {
    // 简化的AUC计算
    const positive = actual.filter(label => label === 1).length;
    const negative = actual.length - positive;
    
    if (positive === 0 || negative === 0) return 0.5;
    
    let auc = 0;
    for (let i = 0; i < actual.length; i++) {
      for (let j = 0; j < actual.length; j++) {
        if (actual[i] === 1 && actual[j] === 0) {
          if (predicted[i] > predicted[j]) {
            auc += 1;
          } else if (predicted[i] === predicted[j]) {
            auc += 0.5;
          }
        }
      }
    }
    
    return auc / (positive * negative);
  }

  private generateValidationReport(
    method: string, sampleCount: number, metrics: any, confusionMatrix: number[][]
  ): any {
    return {
      validationMethod: method,
      sampleCount,
      timestamp: new Date().toISOString(),
      summary: {
        overallAccuracy: metrics.accuracy || 0,
        precision: metrics.precision || 0,
        recall: metrics.recall || 0,
        f1Score: metrics.f1_score || 0
      },
      confusionMatrix: {
        matrix: confusionMatrix,
        truePositives: confusionMatrix[1]?.[1] || 0,
        falsePositives: confusionMatrix[0]?.[1] || 0,
        trueNegatives: confusionMatrix[0]?.[0] || 0,
        falseNegatives: confusionMatrix[1]?.[0] || 0
      },
      recommendations: this.generateRecommendations(metrics)
    };
  }

  private generateRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];
    
    if (metrics.accuracy < 0.7) {
      recommendations.push('模型准确率较低，建议增加训练数据或调整模型架构');
    }
    
    if (metrics.precision < 0.7) {
      recommendations.push('精确率较低，可能存在较多假阳性，建议调整分类阈值');
    }
    
    if (metrics.recall < 0.7) {
      recommendations.push('召回率较低，可能遗漏较多正样本，建议平衡数据集');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('模型性能良好，可以考虑部署使用');
    }
    
    return recommendations;
  }
}
