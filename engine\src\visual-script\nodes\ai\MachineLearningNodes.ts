/**
 * 机器学习节点
 * 实现批次3.3的10个机器学习相关节点
 */

import { VisualScriptNode } from '../../VisualScriptNode';

/**
 * 机器学习节点基类
 */
export abstract class MachineLearningNode extends VisualScriptNode {
  constructor(nodeType: string, name: string) {
    super(nodeType, name);
    this.setupCommonInputs();
    this.setupCommonOutputs();
  }

  protected setupCommonInputs(): void {
    this.addInput('modelId', 'string', '模型ID', '');
    this.addInput('config', 'object', '配置', {});
  }

  protected setupCommonOutputs(): void {
    this.addOutput('result', 'object', '结果');
    this.addOutput('success', 'boolean', '成功');
    this.addOutput('error', 'string', '错误信息');
  }

  protected validateConfig(config: any): boolean {
    return config && typeof config === 'object';
  }
}

/**
 * 1. 强化学习节点
 */
export class ReinforcementLearningNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/reinforcementLearning';
  public static readonly NAME = '强化学习';
  public static readonly DESCRIPTION = '强化学习算法实现';

  private qTable: Map<string, Map<string, number>> = new Map();
  private epsilon: number = 0.1;
  private alpha: number = 0.1;
  private gamma: number = 0.9;

  constructor() {
    super(ReinforcementLearningNode.TYPE, ReinforcementLearningNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('state', 'string', '当前状态', '');
    this.addInput('action', 'string', '动作', '');
    this.addInput('reward', 'number', '奖励', 0);
    this.addInput('nextState', 'string', '下一状态', '');
    this.addInput('availableActions', 'array', '可用动作', []);
    this.addInput('learningRate', 'number', '学习率', 0.1);
    this.addInput('discountFactor', 'number', '折扣因子', 0.9);
    this.addInput('explorationRate', 'number', '探索率', 0.1);
  }

  private setupOutputs(): void {
    this.addOutput('bestAction', 'string', '最佳动作');
    this.addOutput('qValue', 'number', 'Q值');
    this.addOutput('qTable', 'object', 'Q表');
    this.addOutput('explorationAction', 'boolean', '是否探索动作');
  }

  public execute(inputs: any): any {
    try {
      const state = this.getInputValue(inputs, 'state');
      const action = this.getInputValue(inputs, 'action');
      const reward = this.getInputValue(inputs, 'reward');
      const nextState = this.getInputValue(inputs, 'nextState');
      const availableActions = this.getInputValue(inputs, 'availableActions');
      const learningRate = this.getInputValue(inputs, 'learningRate');
      const discountFactor = this.getInputValue(inputs, 'discountFactor');
      const explorationRate = this.getInputValue(inputs, 'explorationRate');

      this.alpha = learningRate;
      this.gamma = discountFactor;
      this.epsilon = explorationRate;

      // 更新Q值（如果提供了动作和奖励）
      if (action && reward !== undefined && nextState) {
        this.updateQValue(state, action, reward, nextState);
      }

      // 选择动作
      const actionResult = this.selectAction(state, availableActions);

      // 获取Q值
      const qValue = this.getQValue(state, actionResult.action);

      return {
        bestAction: actionResult.action,
        qValue,
        qTable: this.serializeQTable(),
        explorationAction: actionResult.isExploration,
        result: { 
          status: 'computed', 
          state,
          action: actionResult.action,
          qValue
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        bestAction: '',
        qValue: 0,
        qTable: {},
        explorationAction: false,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '强化学习计算失败'
      };
    }
  }

  private updateQValue(state: string, action: string, reward: number, nextState: string): void {
    if (!this.qTable.has(state)) {
      this.qTable.set(state, new Map());
    }

    const stateActions = this.qTable.get(state)!;
    const currentQ = stateActions.get(action) || 0;

    // 获取下一状态的最大Q值
    const maxNextQ = this.getMaxQValue(nextState);

    // Q-learning更新公式
    const newQ = currentQ + this.alpha * (reward + this.gamma * maxNextQ - currentQ);
    stateActions.set(action, newQ);
  }

  private selectAction(state: string, availableActions: string[]): any {
    if (!availableActions || availableActions.length === 0) {
      return { action: '', isExploration: false };
    }

    // ε-贪婪策略
    if (Math.random() < this.epsilon) {
      // 探索：随机选择动作
      const randomIndex = Math.floor(Math.random() * availableActions.length);
      return { action: availableActions[randomIndex], isExploration: true };
    } else {
      // 利用：选择Q值最高的动作
      let bestAction = availableActions[0];
      let bestQ = this.getQValue(state, bestAction);

      for (const action of availableActions) {
        const q = this.getQValue(state, action);
        if (q > bestQ) {
          bestQ = q;
          bestAction = action;
        }
      }

      return { action: bestAction, isExploration: false };
    }
  }

  private getQValue(state: string, action: string): number {
    if (!this.qTable.has(state)) {
      return 0;
    }
    return this.qTable.get(state)!.get(action) || 0;
  }

  private getMaxQValue(state: string): number {
    if (!this.qTable.has(state)) {
      return 0;
    }

    const stateActions = this.qTable.get(state)!;
    if (stateActions.size === 0) {
      return 0;
    }

    return Math.max(...Array.from(stateActions.values()));
  }

  private serializeQTable(): any {
    const serialized: any = {};
    for (const [state, actions] of this.qTable.entries()) {
      serialized[state] = {};
      for (const [action, qValue] of actions.entries()) {
        serialized[state][action] = qValue;
      }
    }
    return serialized;
  }
}

/**
 * 2. 联邦学习节点
 */
export class FederatedLearningNode extends MachineLearningNode {
  public static readonly TYPE = 'ml/federatedLearning';
  public static readonly NAME = '联邦学习';
  public static readonly DESCRIPTION = '联邦学习协调和聚合';

  private globalModel: any = null;
  private clientModels: Map<string, any> = new Map();
  private aggregationRound: number = 0;

  constructor() {
    super(FederatedLearningNode.TYPE, FederatedLearningNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('clientId', 'string', '客户端ID', '');
    this.addInput('localModel', 'object', '本地模型', {});
    this.addInput('aggregationMethod', 'string', '聚合方法', 'fedavg');
    this.addInput('clientWeight', 'number', '客户端权重', 1.0);
    this.addInput('minClients', 'number', '最小客户端数', 2);
  }

  private setupOutputs(): void {
    this.addOutput('globalModel', 'object', '全局模型');
    this.addOutput('aggregationRound', 'number', '聚合轮次');
    this.addOutput('participatingClients', 'number', '参与客户端数');
    this.addOutput('convergenceMetric', 'number', '收敛指标');
  }

  public execute(inputs: any): any {
    try {
      const clientId = this.getInputValue(inputs, 'clientId');
      const localModel = this.getInputValue(inputs, 'localModel');
      const aggregationMethod = this.getInputValue(inputs, 'aggregationMethod');
      const clientWeight = this.getInputValue(inputs, 'clientWeight');
      const minClients = this.getInputValue(inputs, 'minClients');

      // 注册客户端模型
      if (clientId && localModel) {
        this.clientModels.set(clientId, { model: localModel, weight: clientWeight });
      }

      // 检查是否可以进行聚合
      if (this.clientModels.size >= minClients) {
        const aggregationResult = this.aggregateModels(aggregationMethod);
        this.globalModel = aggregationResult.model;
        this.aggregationRound++;

        // 清空客户端模型以准备下一轮
        this.clientModels.clear();

        return {
          globalModel: this.globalModel,
          aggregationRound: this.aggregationRound,
          participatingClients: aggregationResult.clientCount,
          convergenceMetric: aggregationResult.convergence,
          result: { 
            status: 'aggregated', 
            round: this.aggregationRound,
            clients: aggregationResult.clientCount
          },
          success: true,
          error: ''
        };
      } else {
        return {
          globalModel: this.globalModel,
          aggregationRound: this.aggregationRound,
          participatingClients: this.clientModels.size,
          convergenceMetric: 0,
          result: { 
            status: 'waiting', 
            currentClients: this.clientModels.size,
            requiredClients: minClients
          },
          success: true,
          error: ''
        };
      }

    } catch (error) {
      return {
        globalModel: null,
        aggregationRound: 0,
        participatingClients: 0,
        convergenceMetric: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '联邦学习失败'
      };
    }
  }

  private aggregateModels(method: string): any {
    const clientData = Array.from(this.clientModels.values());
    const clientCount = clientData.length;

    switch (method) {
      case 'fedavg':
        return this.federatedAveraging(clientData);
      case 'weighted_avg':
        return this.weightedAveraging(clientData);
      default:
        return this.federatedAveraging(clientData);
    }
  }

  private federatedAveraging(clientData: any[]): any {
    if (clientData.length === 0) {
      return { model: null, clientCount: 0, convergence: 0 };
    }

    // 简化的联邦平均算法
    const aggregatedModel: any = {
      parameters: [],
      metadata: {
        aggregationMethod: 'fedavg',
        clientCount: clientData.length,
        timestamp: new Date().toISOString()
      }
    };

    // 假设所有模型具有相同的参数结构
    const firstModel = clientData[0].model;
    if (firstModel.parameters && Array.isArray(firstModel.parameters)) {
      const paramCount = firstModel.parameters.length;
      aggregatedModel.parameters = Array(paramCount).fill(0);

      // 计算参数平均值
      for (let i = 0; i < paramCount; i++) {
        let sum = 0;
        for (const client of clientData) {
          if (client.model.parameters && client.model.parameters[i] !== undefined) {
            sum += client.model.parameters[i];
          }
        }
        aggregatedModel.parameters[i] = sum / clientData.length;
      }
    }

    // 计算收敛指标（简化版本）
    const convergence = this.calculateConvergence(clientData);

    return {
      model: aggregatedModel,
      clientCount: clientData.length,
      convergence
    };
  }

  private weightedAveraging(clientData: any[]): any {
    if (clientData.length === 0) {
      return { model: null, clientCount: 0, convergence: 0 };
    }

    const totalWeight = clientData.reduce((sum, client) => sum + client.weight, 0);
    
    const aggregatedModel: any = {
      parameters: [],
      metadata: {
        aggregationMethod: 'weighted_avg',
        clientCount: clientData.length,
        totalWeight,
        timestamp: new Date().toISOString()
      }
    };

    const firstModel = clientData[0].model;
    if (firstModel.parameters && Array.isArray(firstModel.parameters)) {
      const paramCount = firstModel.parameters.length;
      aggregatedModel.parameters = Array(paramCount).fill(0);

      // 计算加权平均
      for (let i = 0; i < paramCount; i++) {
        let weightedSum = 0;
        for (const client of clientData) {
          if (client.model.parameters && client.model.parameters[i] !== undefined) {
            weightedSum += client.model.parameters[i] * client.weight;
          }
        }
        aggregatedModel.parameters[i] = weightedSum / totalWeight;
      }
    }

    const convergence = this.calculateConvergence(clientData);

    return {
      model: aggregatedModel,
      clientCount: clientData.length,
      convergence
    };
  }

  private calculateConvergence(clientData: any[]): number {
    if (clientData.length < 2) {
      return 1.0;
    }

    // 计算客户端模型之间的差异作为收敛指标
    let totalDifference = 0;
    let comparisonCount = 0;

    for (let i = 0; i < clientData.length - 1; i++) {
      for (let j = i + 1; j < clientData.length; j++) {
        const diff = this.calculateModelDifference(
          clientData[i].model,
          clientData[j].model
        );
        totalDifference += diff;
        comparisonCount++;
      }
    }

    const avgDifference = comparisonCount > 0 ? totalDifference / comparisonCount : 0;
    return Math.max(0, 1 - avgDifference); // 转换为收敛指标（越接近1越收敛）
  }

  private calculateModelDifference(model1: any, model2: any): number {
    if (!model1.parameters || !model2.parameters) {
      return 1.0;
    }

    let sumSquaredDiff = 0;
    const paramCount = Math.min(model1.parameters.length, model2.parameters.length);

    for (let i = 0; i < paramCount; i++) {
      const diff = model1.parameters[i] - model2.parameters[i];
      sumSquaredDiff += diff * diff;
    }

    return Math.sqrt(sumSquaredDiff / paramCount);
  }
}
