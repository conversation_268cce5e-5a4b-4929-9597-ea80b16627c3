/**
 * 批次3.3节点注册表
 * 深度学习和机器学习节点注册
 */

import { VisualScriptNode } from '../../visualscript/VisualScriptNode';

// 导入深度学习节点
import {
  DeepLearningModelNode,
  NeuralNetworkNode,
  ConvolutionalNetworkNode,
  RecurrentNetworkNode
} from '../nodes/ai/DeepLearningNodes';

import {
  TransformerModelNode,
  GANModelNode
} from '../nodes/ai/DeepLearningNodes2';

import {
  VAEModelNode,
  AttentionMechanismNode
} from '../nodes/ai/DeepLearningNodes3';

import {
  EmbeddingLayerNode,
  DropoutLayerNode,
  BatchNormalizationNode,
  ActivationFunctionNode
} from '../nodes/ai/DeepLearningNodes4';

import {
  LossFunctionNode,
  OptimizerNode,
  RegularizationNode
} from '../nodes/ai/DeepLearningNodes5';

// 导入机器学习节点
import {
  ReinforcementLearningNode,
  FederatedLearningNode
} from '../nodes/ai/MachineLearningNodes';

import {
  TransferLearningNode,
  ModelEnsembleNode
} from '../nodes/ai/MachineLearningNodes2';

import {
  HyperparameterTuningNode,
  ModelValidationNode
} from '../nodes/ai/MachineLearningNodes3';

import {
  CrossValidationNode,
  FeatureSelectionNode,
  DimensionalityReductionNode,
  ClusteringNode
} from '../nodes/ai/MachineLearningNodes4';

/**
 * 批次3.3节点注册表类
 */
export class Batch33NodesRegistry {
  private static instance: Batch33NodesRegistry;
  private registeredNodes: Map<string, any> = new Map();

  private constructor() {
    this.registerAllNodes();
  }

  public static getInstance(): Batch33NodesRegistry {
    if (!Batch33NodesRegistry.instance) {
      Batch33NodesRegistry.instance = new Batch33NodesRegistry();
    }
    return Batch33NodesRegistry.instance;
  }

  /**
   * 注册所有批次3.3节点
   */
  private registerAllNodes(): void {
    // 注册深度学习节点
    this.registerDeepLearningNodes();
    
    // 注册机器学习节点
    this.registerMachineLearningNodes();
  }

  /**
   * 注册深度学习节点
   */
  private registerDeepLearningNodes(): void {
    // 基础深度学习节点
    this.registerNode(DeepLearningModelNode.TYPE, DeepLearningModelNode, {
      category: '深度学习',
      description: DeepLearningModelNode.DESCRIPTION,
      icon: 'brain',
      color: '#FF6B35'
    });

    this.registerNode(NeuralNetworkNode.TYPE, NeuralNetworkNode, {
      category: '深度学习',
      description: NeuralNetworkNode.DESCRIPTION,
      icon: 'network',
      color: '#FF6B35'
    });

    this.registerNode(ConvolutionalNetworkNode.TYPE, ConvolutionalNetworkNode, {
      category: '深度学习',
      description: ConvolutionalNetworkNode.DESCRIPTION,
      icon: 'grid',
      color: '#FF6B35'
    });

    this.registerNode(RecurrentNetworkNode.TYPE, RecurrentNetworkNode, {
      category: '深度学习',
      description: RecurrentNetworkNode.DESCRIPTION,
      icon: 'repeat',
      color: '#FF6B35'
    });

    // 高级深度学习节点
    this.registerNode(TransformerModelNode.TYPE, TransformerModelNode, {
      category: '深度学习',
      description: TransformerModelNode.DESCRIPTION,
      icon: 'transformer',
      color: '#FF6B35'
    });

    this.registerNode(GANModelNode.TYPE, GANModelNode, {
      category: '深度学习',
      description: GANModelNode.DESCRIPTION,
      icon: 'generate',
      color: '#FF6B35'
    });

    this.registerNode(VAEModelNode.TYPE, VAEModelNode, {
      category: '深度学习',
      description: VAEModelNode.DESCRIPTION,
      icon: 'encode',
      color: '#FF6B35'
    });

    this.registerNode(AttentionMechanismNode.TYPE, AttentionMechanismNode, {
      category: '深度学习',
      description: AttentionMechanismNode.DESCRIPTION,
      icon: 'focus',
      color: '#FF6B35'
    });

    // 深度学习组件节点
    this.registerNode(EmbeddingLayerNode.TYPE, EmbeddingLayerNode, {
      category: '深度学习',
      description: EmbeddingLayerNode.DESCRIPTION,
      icon: 'embed',
      color: '#FF8C42'
    });

    this.registerNode(DropoutLayerNode.TYPE, DropoutLayerNode, {
      category: '深度学习',
      description: DropoutLayerNode.DESCRIPTION,
      icon: 'dropout',
      color: '#FF8C42'
    });

    this.registerNode(BatchNormalizationNode.TYPE, BatchNormalizationNode, {
      category: '深度学习',
      description: BatchNormalizationNode.DESCRIPTION,
      icon: 'normalize',
      color: '#FF8C42'
    });

    this.registerNode(ActivationFunctionNode.TYPE, ActivationFunctionNode, {
      category: '深度学习',
      description: ActivationFunctionNode.DESCRIPTION,
      icon: 'function',
      color: '#FF8C42'
    });

    // 深度学习训练节点
    this.registerNode(LossFunctionNode.TYPE, LossFunctionNode, {
      category: '深度学习',
      description: LossFunctionNode.DESCRIPTION,
      icon: 'loss',
      color: '#FFA726'
    });

    this.registerNode(OptimizerNode.TYPE, OptimizerNode, {
      category: '深度学习',
      description: OptimizerNode.DESCRIPTION,
      icon: 'optimize',
      color: '#FFA726'
    });

    this.registerNode(RegularizationNode.TYPE, RegularizationNode, {
      category: '深度学习',
      description: RegularizationNode.DESCRIPTION,
      icon: 'regularize',
      color: '#FFA726'
    });
  }

  /**
   * 注册机器学习节点
   */
  private registerMachineLearningNodes(): void {
    // 高级机器学习算法
    this.registerNode(ReinforcementLearningNode.TYPE, ReinforcementLearningNode, {
      category: '机器学习',
      description: ReinforcementLearningNode.DESCRIPTION,
      icon: 'reward',
      color: '#66BB6A'
    });

    this.registerNode(FederatedLearningNode.TYPE, FederatedLearningNode, {
      category: '机器学习',
      description: FederatedLearningNode.DESCRIPTION,
      icon: 'federated',
      color: '#66BB6A'
    });

    this.registerNode(TransferLearningNode.TYPE, TransferLearningNode, {
      category: '机器学习',
      description: TransferLearningNode.DESCRIPTION,
      icon: 'transfer',
      color: '#66BB6A'
    });

    this.registerNode(ModelEnsembleNode.TYPE, ModelEnsembleNode, {
      category: '机器学习',
      description: ModelEnsembleNode.DESCRIPTION,
      icon: 'ensemble',
      color: '#66BB6A'
    });

    // 模型优化和验证
    this.registerNode(HyperparameterTuningNode.TYPE, HyperparameterTuningNode, {
      category: '机器学习',
      description: HyperparameterTuningNode.DESCRIPTION,
      icon: 'tune',
      color: '#42A5F5'
    });

    this.registerNode(ModelValidationNode.TYPE, ModelValidationNode, {
      category: '机器学习',
      description: ModelValidationNode.DESCRIPTION,
      icon: 'validate',
      color: '#42A5F5'
    });

    this.registerNode(CrossValidationNode.TYPE, CrossValidationNode, {
      category: '机器学习',
      description: CrossValidationNode.DESCRIPTION,
      icon: 'cross-validate',
      color: '#42A5F5'
    });

    // 数据处理和分析
    this.registerNode(FeatureSelectionNode.TYPE, FeatureSelectionNode, {
      category: '机器学习',
      description: FeatureSelectionNode.DESCRIPTION,
      icon: 'select',
      color: '#AB47BC'
    });

    this.registerNode(DimensionalityReductionNode.TYPE, DimensionalityReductionNode, {
      category: '机器学习',
      description: DimensionalityReductionNode.DESCRIPTION,
      icon: 'reduce',
      color: '#AB47BC'
    });

    this.registerNode(ClusteringNode.TYPE, ClusteringNode, {
      category: '机器学习',
      description: ClusteringNode.DESCRIPTION,
      icon: 'cluster',
      color: '#AB47BC'
    });
  }

  /**
   * 注册单个节点
   */
  private registerNode(type: string, nodeClass: any, metadata: any): void {
    this.registeredNodes.set(type, {
      type,
      nodeClass,
      metadata
    });
  }

  /**
   * 获取节点类
   */
  public getNodeClass(type: string): any {
    const nodeInfo = this.registeredNodes.get(type);
    return nodeInfo ? nodeInfo.nodeClass : null;
  }

  /**
   * 获取节点元数据
   */
  public getNodeMetadata(type: string): any {
    const nodeInfo = this.registeredNodes.get(type);
    return nodeInfo ? nodeInfo.metadata : null;
  }

  /**
   * 创建节点实例
   */
  public createNode(type: string): VisualScriptNode | null {
    const NodeClass = this.getNodeClass(type);
    if (NodeClass) {
      return new NodeClass();
    }
    return null;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 深度学习节点（15个）
      'ai/deepLearningModel',
      'ai/neuralNetwork',
      'ai/convolutionalNetwork',
      'ai/recurrentNetwork',
      'ai/transformerModel',
      'ai/ganModel',
      'ai/vaeModel',
      'ai/attentionMechanism',
      'ai/embeddingLayer',
      'ai/dropoutLayer',
      'ai/batchNormalization',
      'ai/activationFunction',
      'ai/lossFunction',
      'ai/optimizer',
      'ai/regularization',

      // 机器学习节点（10个）
      'ml/reinforcementLearning',
      'ml/federatedLearning',
      'ml/transferLearning',
      'ml/modelEnsemble',
      'ml/hyperparameterTuning',
      'ml/modelValidation',
      'ml/crossValidation',
      'ml/featureSelection',
      'ml/dimensionalityReduction',
      'ml/clustering'
    ];
  }

  /**
   * 获取节点分类统计
   */
  public getNodeCategoryStats(): any {
    return {
      '深度学习': 15,
      '机器学习': 10,
      '总计': 25
    };
  }
}

// 创建单例实例
export const batch33NodesRegistry = Batch33NodesRegistry.getInstance();
