/**
 * 深度学习节点
 * 实现批次3.3的15个深度学习相关节点
 */

import { VisualScriptNode } from '../../VisualScriptNode';

/**
 * 深度学习模型配置接口
 */
export interface DeepLearningModelConfig {
  modelId: string;
  modelType: string;
  architecture: any;
  hyperparameters: any;
  trainingConfig?: any;
}

/**
 * 深度学习节点基类
 */
export abstract class DeepLearningNode extends VisualScriptNode {
  constructor(nodeType: string, name: string) {
    super(nodeType, name);
    this.setupCommonInputs();
    this.setupCommonOutputs();
  }

  protected setupCommonInputs(): void {
    this.addInput('modelId', 'string', '模型ID', '');
    this.addInput('config', 'object', '配置', {});
  }

  protected setupCommonOutputs(): void {
    this.addOutput('result', 'object', '结果');
    this.addOutput('success', 'boolean', '成功');
    this.addOutput('error', 'string', '错误信息');
  }

  protected validateModelConfig(config: any): boolean {
    return config && typeof config === 'object';
  }
}

/**
 * 1. 深度学习模型节点
 */
export class DeepLearningModelNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/deepLearningModel';
  public static readonly NAME = '深度学习模型';
  public static readonly DESCRIPTION = '创建和管理深度学习模型';

  constructor() {
    super(DeepLearningModelNode.TYPE, DeepLearningModelNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('modelType', 'string', '模型类型', 'feedforward');
    this.addInput('inputSize', 'number', '输入维度', 784);
    this.addInput('outputSize', 'number', '输出维度', 10);
    this.addInput('hiddenLayers', 'array', '隐藏层配置', [128, 64]);
    this.addInput('activation', 'string', '激活函数', 'relu');
  }

  private setupOutputs(): void {
    this.addOutput('model', 'object', '模型对象');
    this.addOutput('modelInfo', 'object', '模型信息');
  }

  public execute(inputs: any): any {
    try {
      const modelId = this.getInputValue(inputs, 'modelId');
      const modelType = this.getInputValue(inputs, 'modelType');
      const inputSize = this.getInputValue(inputs, 'inputSize');
      const outputSize = this.getInputValue(inputs, 'outputSize');
      const hiddenLayers = this.getInputValue(inputs, 'hiddenLayers');
      const activation = this.getInputValue(inputs, 'activation');

      // 创建模型配置
      const modelConfig: DeepLearningModelConfig = {
        modelId,
        modelType,
        architecture: {
          inputSize,
          outputSize,
          hiddenLayers,
          activation
        },
        hyperparameters: {
          learningRate: 0.001,
          batchSize: 32,
          epochs: 100
        }
      };

      // 模拟创建深度学习模型
      const model = this.createModel(modelConfig);
      const modelInfo = {
        id: modelId,
        type: modelType,
        parameters: this.calculateParameters(modelConfig),
        layers: hiddenLayers.length + 2, // 输入层 + 隐藏层 + 输出层
        createdAt: new Date().toISOString()
      };

      return {
        model,
        modelInfo,
        result: { modelId, status: 'created' },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        model: null,
        modelInfo: null,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '深度学习模型创建失败'
      };
    }
  }

  private createModel(config: DeepLearningModelConfig): any {
    // 模拟模型创建逻辑
    return {
      id: config.modelId,
      type: config.modelType,
      architecture: config.architecture,
      weights: this.initializeWeights(config.architecture),
      compiled: false
    };
  }

  private initializeWeights(architecture: any): any {
    // 模拟权重初始化
    const weights = [];
    let prevSize = architecture.inputSize;
    
    for (const layerSize of architecture.hiddenLayers) {
      weights.push({
        shape: [prevSize, layerSize],
        values: Array(prevSize * layerSize).fill(0).map(() => Math.random() * 0.1 - 0.05)
      });
      prevSize = layerSize;
    }
    
    // 输出层权重
    weights.push({
      shape: [prevSize, architecture.outputSize],
      values: Array(prevSize * architecture.outputSize).fill(0).map(() => Math.random() * 0.1 - 0.05)
    });
    
    return weights;
  }

  private calculateParameters(config: DeepLearningModelConfig): number {
    let params = 0;
    let prevSize = config.architecture.inputSize;
    
    for (const layerSize of config.architecture.hiddenLayers) {
      params += prevSize * layerSize + layerSize; // 权重 + 偏置
      prevSize = layerSize;
    }
    
    params += prevSize * config.architecture.outputSize + config.architecture.outputSize;
    return params;
  }
}

/**
 * 2. 神经网络节点
 */
export class NeuralNetworkNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/neuralNetwork';
  public static readonly NAME = '神经网络';
  public static readonly DESCRIPTION = '通用神经网络实现';

  constructor() {
    super(NeuralNetworkNode.TYPE, NeuralNetworkNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('weights', 'array', '权重', []);
    this.addInput('biases', 'array', '偏置', []);
    this.addInput('activationFunction', 'string', '激活函数', 'sigmoid');
  }

  private setupOutputs(): void {
    this.addOutput('output', 'array', '输出');
    this.addOutput('activations', 'array', '激活值');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const weights = this.getInputValue(inputs, 'weights');
      const biases = this.getInputValue(inputs, 'biases');
      const activationFunction = this.getInputValue(inputs, 'activationFunction');

      if (!Array.isArray(inputData) || inputData.length === 0) {
        throw new Error('输入数据无效');
      }

      // 前向传播
      const result = this.forwardPass(inputData, weights, biases, activationFunction);

      return {
        output: result.output,
        activations: result.activations,
        result: { status: 'computed', outputSize: result.output.length },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        output: [],
        activations: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '神经网络计算失败'
      };
    }
  }

  private forwardPass(input: number[], weights: number[][], biases: number[], activationFn: string): any {
    let currentInput = [...input];
    const activations = [currentInput];

    // 逐层计算
    for (let layer = 0; layer < weights.length; layer++) {
      const layerWeights = weights[layer];
      const layerBiases = biases[layer] || [];
      const layerOutput = [];

      for (let neuron = 0; neuron < layerWeights[0].length; neuron++) {
        let sum = layerBiases[neuron] || 0;
        
        for (let input_idx = 0; input_idx < currentInput.length; input_idx++) {
          sum += currentInput[input_idx] * layerWeights[input_idx][neuron];
        }

        layerOutput.push(this.applyActivation(sum, activationFn));
      }

      currentInput = layerOutput;
      activations.push([...layerOutput]);
    }

    return {
      output: currentInput,
      activations
    };
  }

  private applyActivation(x: number, activationFn: string): number {
    switch (activationFn) {
      case 'sigmoid':
        return 1 / (1 + Math.exp(-x));
      case 'tanh':
        return Math.tanh(x);
      case 'relu':
        return Math.max(0, x);
      case 'leaky_relu':
        return x > 0 ? x : 0.01 * x;
      default:
        return x;
    }
  }
}

/**
 * 3. 卷积神经网络节点
 */
export class ConvolutionalNetworkNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/convolutionalNetwork';
  public static readonly NAME = '卷积神经网络';
  public static readonly DESCRIPTION = 'CNN卷积神经网络实现';

  constructor() {
    super(ConvolutionalNetworkNode.TYPE, ConvolutionalNetworkNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputImage', 'array', '输入图像', []);
    this.addInput('filters', 'array', '卷积核', []);
    this.addInput('kernelSize', 'number', '卷积核大小', 3);
    this.addInput('stride', 'number', '步长', 1);
    this.addInput('padding', 'string', '填充方式', 'same');
    this.addInput('poolingType', 'string', '池化类型', 'max');
    this.addInput('poolingSize', 'number', '池化大小', 2);
  }

  private setupOutputs(): void {
    this.addOutput('featureMaps', 'array', '特征图');
    this.addOutput('pooledMaps', 'array', '池化后特征图');
    this.addOutput('outputShape', 'array', '输出形状');
  }

  public execute(inputs: any): any {
    try {
      const inputImage = this.getInputValue(inputs, 'inputImage');
      const filters = this.getInputValue(inputs, 'filters');
      const kernelSize = this.getInputValue(inputs, 'kernelSize');
      const stride = this.getInputValue(inputs, 'stride');
      const padding = this.getInputValue(inputs, 'padding');
      const poolingType = this.getInputValue(inputs, 'poolingType');
      const poolingSize = this.getInputValue(inputs, 'poolingSize');

      if (!Array.isArray(inputImage) || inputImage.length === 0) {
        throw new Error('输入图像无效');
      }

      // 卷积操作
      const featureMaps = this.convolution(inputImage, filters, kernelSize, stride, padding);

      // 池化操作
      const pooledMaps = this.pooling(featureMaps, poolingType, poolingSize);

      const outputShape = this.calculateOutputShape(inputImage, kernelSize, stride, padding, poolingSize);

      return {
        featureMaps,
        pooledMaps,
        outputShape,
        result: {
          status: 'computed',
          featureMapCount: featureMaps.length,
          outputDimensions: outputShape
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        featureMaps: [],
        pooledMaps: [],
        outputShape: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : 'CNN计算失败'
      };
    }
  }

  private convolution(input: number[][], filters: number[][][], kernelSize: number, stride: number, padding: string): number[][][] {
    // 简化的卷积实现
    const featureMaps: number[][][] = [];

    for (const filter of filters) {
      const featureMap = this.applyFilter(input, filter, kernelSize, stride, padding);
      featureMaps.push(featureMap);
    }

    return featureMaps;
  }

  private applyFilter(input: number[][], filter: number[][], kernelSize: number, stride: number, padding: string): number[][] {
    const inputHeight = input.length;
    const inputWidth = input[0].length;

    // 计算输出尺寸
    const outputHeight = Math.floor((inputHeight - kernelSize + 2 * (padding === 'same' ? 1 : 0)) / stride) + 1;
    const outputWidth = Math.floor((inputWidth - kernelSize + 2 * (padding === 'same' ? 1 : 0)) / stride) + 1;

    const output: number[][] = Array(outputHeight).fill(0).map(() => Array(outputWidth).fill(0));

    for (let i = 0; i < outputHeight; i++) {
      for (let j = 0; j < outputWidth; j++) {
        let sum = 0;
        for (let fi = 0; fi < kernelSize; fi++) {
          for (let fj = 0; fj < kernelSize; fj++) {
            const inputRow = i * stride + fi - (padding === 'same' ? 1 : 0);
            const inputCol = j * stride + fj - (padding === 'same' ? 1 : 0);

            if (inputRow >= 0 && inputRow < inputHeight && inputCol >= 0 && inputCol < inputWidth) {
              sum += input[inputRow][inputCol] * filter[fi][fj];
            }
          }
        }
        output[i][j] = Math.max(0, sum); // ReLU激活
      }
    }

    return output;
  }

  private pooling(featureMaps: number[][][], poolingType: string, poolingSize: number): number[][][] {
    return featureMaps.map(featureMap => this.applyPooling(featureMap, poolingType, poolingSize));
  }

  private applyPooling(featureMap: number[][], poolingType: string, poolingSize: number): number[][] {
    const inputHeight = featureMap.length;
    const inputWidth = featureMap[0].length;

    const outputHeight = Math.floor(inputHeight / poolingSize);
    const outputWidth = Math.floor(inputWidth / poolingSize);

    const output: number[][] = Array(outputHeight).fill(0).map(() => Array(outputWidth).fill(0));

    for (let i = 0; i < outputHeight; i++) {
      for (let j = 0; j < outputWidth; j++) {
        const values = [];
        for (let pi = 0; pi < poolingSize; pi++) {
          for (let pj = 0; pj < poolingSize; pj++) {
            const row = i * poolingSize + pi;
            const col = j * poolingSize + pj;
            if (row < inputHeight && col < inputWidth) {
              values.push(featureMap[row][col]);
            }
          }
        }

        if (poolingType === 'max') {
          output[i][j] = Math.max(...values);
        } else if (poolingType === 'avg') {
          output[i][j] = values.reduce((a, b) => a + b, 0) / values.length;
        }
      }
    }

    return output;
  }

  private calculateOutputShape(input: number[][], kernelSize: number, stride: number, padding: string, poolingSize: number): number[] {
    const inputHeight = input.length;
    const inputWidth = input[0].length;

    const convHeight = Math.floor((inputHeight - kernelSize + 2 * (padding === 'same' ? 1 : 0)) / stride) + 1;
    const convWidth = Math.floor((inputWidth - kernelSize + 2 * (padding === 'same' ? 1 : 0)) / stride) + 1;

    const poolHeight = Math.floor(convHeight / poolingSize);
    const poolWidth = Math.floor(convWidth / poolingSize);

    return [poolHeight, poolWidth];
  }
}

/**
 * 4. 循环神经网络节点
 */
export class RecurrentNetworkNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/recurrentNetwork';
  public static readonly NAME = '循环神经网络';
  public static readonly DESCRIPTION = 'RNN循环神经网络实现';

  constructor() {
    super(RecurrentNetworkNode.TYPE, RecurrentNetworkNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('sequenceData', 'array', '序列数据', []);
    this.addInput('hiddenSize', 'number', '隐藏层大小', 128);
    this.addInput('sequenceLength', 'number', '序列长度', 10);
    this.addInput('returnSequences', 'boolean', '返回序列', false);
    this.addInput('weights', 'object', '权重', {});
  }

  private setupOutputs(): void {
    this.addOutput('hiddenStates', 'array', '隐藏状态');
    this.addOutput('outputs', 'array', '输出序列');
    this.addOutput('finalOutput', 'array', '最终输出');
  }

  public execute(inputs: any): any {
    try {
      const sequenceData = this.getInputValue(inputs, 'sequenceData');
      const hiddenSize = this.getInputValue(inputs, 'hiddenSize');
      const sequenceLength = this.getInputValue(inputs, 'sequenceLength');
      const returnSequences = this.getInputValue(inputs, 'returnSequences');
      const weights = this.getInputValue(inputs, 'weights');

      if (!Array.isArray(sequenceData) || sequenceData.length === 0) {
        throw new Error('序列数据无效');
      }

      // RNN前向传播
      const result = this.forwardRNN(sequenceData, hiddenSize, sequenceLength, returnSequences, weights);

      return {
        hiddenStates: result.hiddenStates,
        outputs: result.outputs,
        finalOutput: result.finalOutput,
        result: {
          status: 'computed',
          sequenceLength: result.outputs.length,
          hiddenSize
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        hiddenStates: [],
        outputs: [],
        finalOutput: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : 'RNN计算失败'
      };
    }
  }

  private forwardRNN(sequence: number[][], hiddenSize: number, seqLength: number, returnSeq: boolean, weights: any): any {
    const inputSize = sequence[0].length;
    const hiddenStates: number[][] = [];
    const outputs: number[][] = [];

    // 初始化隐藏状态
    let hiddenState = Array(hiddenSize).fill(0);

    // 初始化权重（如果未提供）
    const Wxh = weights.Wxh || this.initializeMatrix(inputSize, hiddenSize);
    const Whh = weights.Whh || this.initializeMatrix(hiddenSize, hiddenSize);
    const Why = weights.Why || this.initializeMatrix(hiddenSize, inputSize);
    const bh = weights.bh || Array(hiddenSize).fill(0);
    const by = weights.by || Array(inputSize).fill(0);

    // 逐时间步处理
    for (let t = 0; t < Math.min(sequence.length, seqLength); t++) {
      const input = sequence[t];

      // 计算新的隐藏状态
      const newHiddenState = this.computeHiddenState(input, hiddenState, Wxh, Whh, bh);
      hiddenStates.push([...newHiddenState]);

      // 计算输出
      const output = this.computeOutput(newHiddenState, Why, by);
      outputs.push(output);

      hiddenState = newHiddenState;
    }

    return {
      hiddenStates,
      outputs: returnSeq ? outputs : [outputs[outputs.length - 1]],
      finalOutput: outputs[outputs.length - 1] || []
    };
  }

  private computeHiddenState(input: number[], prevHidden: number[], Wxh: number[][], Whh: number[][], bh: number[]): number[] {
    const hiddenSize = prevHidden.length;
    const newHidden = Array(hiddenSize).fill(0);

    for (let i = 0; i < hiddenSize; i++) {
      let sum = bh[i];

      // 输入到隐藏层的连接
      for (let j = 0; j < input.length; j++) {
        sum += input[j] * Wxh[j][i];
      }

      // 隐藏层到隐藏层的连接
      for (let j = 0; j < hiddenSize; j++) {
        sum += prevHidden[j] * Whh[j][i];
      }

      newHidden[i] = Math.tanh(sum); // tanh激活函数
    }

    return newHidden;
  }

  private computeOutput(hidden: number[], Why: number[][], by: number[]): number[] {
    const outputSize = by.length;
    const output = Array(outputSize).fill(0);

    for (let i = 0; i < outputSize; i++) {
      let sum = by[i];
      for (let j = 0; j < hidden.length; j++) {
        sum += hidden[j] * Why[j][i];
      }
      output[i] = sum;
    }

    return output;
  }

  private initializeMatrix(rows: number, cols: number): number[][] {
    const matrix: number[][] = [];
    for (let i = 0; i < rows; i++) {
      matrix[i] = [];
      for (let j = 0; j < cols; j++) {
        matrix[i][j] = (Math.random() - 0.5) * 0.1;
      }
    }
    return matrix;
  }
}
