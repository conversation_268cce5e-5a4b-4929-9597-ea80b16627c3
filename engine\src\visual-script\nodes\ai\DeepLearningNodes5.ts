/**
 * 深度学习节点 - 第五部分
 * 完成批次3.3的深度学习节点（13-15）
 */

import { VisualScriptNode } from '../../VisualScriptNode';
import { DeepLearningNode } from './DeepLearningNodes';

/**
 * 13. 损失函数节点
 */
export class LossFunctionNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/lossFunction';
  public static readonly NAME = '损失函数';
  public static readonly DESCRIPTION = '各种损失函数实现';

  constructor() {
    super(LossFunctionNode.TYPE, LossFunctionNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('predictions', 'array', '预测值', []);
    this.addInput('targets', 'array', '目标值', []);
    this.addInput('lossType', 'string', '损失函数类型', 'mse');
    this.addInput('reduction', 'string', '归约方式', 'mean');
  }

  private setupOutputs(): void {
    this.addOutput('loss', 'number', '损失值');
    this.addOutput('gradient', 'array', '梯度');
    this.addOutput('itemLosses', 'array', '逐项损失');
  }

  public execute(inputs: any): any {
    try {
      const predictions = this.getInputValue(inputs, 'predictions');
      const targets = this.getInputValue(inputs, 'targets');
      const lossType = this.getInputValue(inputs, 'lossType');
      const reduction = this.getInputValue(inputs, 'reduction');

      if (!Array.isArray(predictions) || !Array.isArray(targets)) {
        throw new Error('预测值或目标值无效');
      }

      if (predictions.length !== targets.length) {
        throw new Error('预测值和目标值长度不匹配');
      }

      const result = this.computeLoss(predictions, targets, lossType, reduction);

      return {
        loss: result.loss,
        gradient: result.gradient,
        itemLosses: result.itemLosses,
        result: { 
          status: 'computed', 
          lossType,
          reduction,
          sampleCount: predictions.length
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        loss: 0,
        gradient: [],
        itemLosses: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '损失函数计算失败'
      };
    }
  }

  private computeLoss(predictions: number[], targets: number[], lossType: string, reduction: string): any {
    const itemLosses: number[] = [];
    const gradient: number[] = [];

    for (let i = 0; i < predictions.length; i++) {
      const pred = predictions[i];
      const target = targets[i];
      
      switch (lossType) {
        case 'mse': // 均方误差
          const mseDiff = pred - target;
          itemLosses.push(mseDiff * mseDiff);
          gradient.push(2 * mseDiff);
          break;
          
        case 'mae': // 平均绝对误差
          const maeDiff = pred - target;
          itemLosses.push(Math.abs(maeDiff));
          gradient.push(maeDiff > 0 ? 1 : -1);
          break;
          
        case 'cross_entropy': // 交叉熵
          const clampedPred = Math.max(1e-7, Math.min(1 - 1e-7, pred));
          itemLosses.push(-target * Math.log(clampedPred) - (1 - target) * Math.log(1 - clampedPred));
          gradient.push((clampedPred - target) / (clampedPred * (1 - clampedPred)));
          break;
          
        case 'huber': // Huber损失
          const huberDiff = pred - target;
          const delta = 1.0;
          if (Math.abs(huberDiff) <= delta) {
            itemLosses.push(0.5 * huberDiff * huberDiff);
            gradient.push(huberDiff);
          } else {
            itemLosses.push(delta * Math.abs(huberDiff) - 0.5 * delta * delta);
            gradient.push(delta * Math.sign(huberDiff));
          }
          break;
          
        default:
          const defaultDiff = pred - target;
          itemLosses.push(defaultDiff * defaultDiff);
          gradient.push(2 * defaultDiff);
      }
    }

    // 应用归约
    let loss: number;
    switch (reduction) {
      case 'mean':
        loss = itemLosses.reduce((sum, val) => sum + val, 0) / itemLosses.length;
        break;
      case 'sum':
        loss = itemLosses.reduce((sum, val) => sum + val, 0);
        break;
      case 'none':
        loss = itemLosses[0] || 0;
        break;
      default:
        loss = itemLosses.reduce((sum, val) => sum + val, 0) / itemLosses.length;
    }

    return { loss, gradient, itemLosses };
  }
}

/**
 * 14. 优化器节点
 */
export class OptimizerNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/optimizer';
  public static readonly NAME = '优化器';
  public static readonly DESCRIPTION = '各种优化器实现';

  private momentumBuffer: Map<string, number[]> = new Map();
  private velocityBuffer: Map<string, number[]> = new Map();
  private stepCount: number = 0;

  constructor() {
    super(OptimizerNode.TYPE, OptimizerNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('parameters', 'array', '参数', []);
    this.addInput('gradients', 'array', '梯度', []);
    this.addInput('optimizerType', 'string', '优化器类型', 'adam');
    this.addInput('learningRate', 'number', '学习率', 0.001);
    this.addInput('momentum', 'number', '动量', 0.9);
    this.addInput('beta1', 'number', 'Beta1', 0.9);
    this.addInput('beta2', 'number', 'Beta2', 0.999);
    this.addInput('epsilon', 'number', 'Epsilon', 1e-8);
  }

  private setupOutputs(): void {
    this.addOutput('updatedParameters', 'array', '更新后参数');
    this.addOutput('stepSize', 'number', '步长');
    this.addOutput('gradientNorm', 'number', '梯度范数');
  }

  public execute(inputs: any): any {
    try {
      const parameters = this.getInputValue(inputs, 'parameters');
      const gradients = this.getInputValue(inputs, 'gradients');
      const optimizerType = this.getInputValue(inputs, 'optimizerType');
      const learningRate = this.getInputValue(inputs, 'learningRate');
      const momentum = this.getInputValue(inputs, 'momentum');
      const beta1 = this.getInputValue(inputs, 'beta1');
      const beta2 = this.getInputValue(inputs, 'beta2');
      const epsilon = this.getInputValue(inputs, 'epsilon');

      if (!Array.isArray(parameters) || !Array.isArray(gradients)) {
        throw new Error('参数或梯度无效');
      }

      if (parameters.length !== gradients.length) {
        throw new Error('参数和梯度长度不匹配');
      }

      this.stepCount++;
      
      const result = this.updateParameters(
        parameters, gradients, optimizerType, 
        learningRate, momentum, beta1, beta2, epsilon
      );

      return {
        updatedParameters: result.parameters,
        stepSize: result.stepSize,
        gradientNorm: result.gradientNorm,
        result: { 
          status: 'updated', 
          optimizerType,
          stepCount: this.stepCount,
          learningRate
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        updatedParameters: [],
        stepSize: 0,
        gradientNorm: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '优化器更新失败'
      };
    }
  }

  private updateParameters(
    params: number[], grads: number[], optimizerType: string,
    lr: number, momentum: number, beta1: number, beta2: number, epsilon: number
  ): any {
    const updatedParams: number[] = [];
    const gradientNorm = Math.sqrt(grads.reduce((sum, g) => sum + g * g, 0));
    let stepSize = lr;

    switch (optimizerType) {
      case 'sgd':
        for (let i = 0; i < params.length; i++) {
          updatedParams.push(params[i] - lr * grads[i]);
        }
        break;

      case 'momentum':
        const momentumKey = 'momentum';
        if (!this.momentumBuffer.has(momentumKey)) {
          this.momentumBuffer.set(momentumKey, Array(params.length).fill(0));
        }
        const momentumVel = this.momentumBuffer.get(momentumKey)!;
        
        for (let i = 0; i < params.length; i++) {
          momentumVel[i] = momentum * momentumVel[i] + lr * grads[i];
          updatedParams.push(params[i] - momentumVel[i]);
        }
        break;

      case 'adam':
        const adamMKey = 'adam_m';
        const adamVKey = 'adam_v';
        
        if (!this.momentumBuffer.has(adamMKey)) {
          this.momentumBuffer.set(adamMKey, Array(params.length).fill(0));
          this.velocityBuffer.set(adamVKey, Array(params.length).fill(0));
        }
        
        const m = this.momentumBuffer.get(adamMKey)!;
        const v = this.velocityBuffer.get(adamVKey)!;
        
        for (let i = 0; i < params.length; i++) {
          m[i] = beta1 * m[i] + (1 - beta1) * grads[i];
          v[i] = beta2 * v[i] + (1 - beta2) * grads[i] * grads[i];
          
          const mHat = m[i] / (1 - Math.pow(beta1, this.stepCount));
          const vHat = v[i] / (1 - Math.pow(beta2, this.stepCount));
          
          updatedParams.push(params[i] - lr * mHat / (Math.sqrt(vHat) + epsilon));
        }
        
        stepSize = lr / (1 - Math.pow(beta1, this.stepCount));
        break;

      case 'rmsprop':
        const rmspropKey = 'rmsprop';
        if (!this.velocityBuffer.has(rmspropKey)) {
          this.velocityBuffer.set(rmspropKey, Array(params.length).fill(0));
        }
        const rmspropV = this.velocityBuffer.get(rmspropKey)!;
        
        for (let i = 0; i < params.length; i++) {
          rmspropV[i] = beta2 * rmspropV[i] + (1 - beta2) * grads[i] * grads[i];
          updatedParams.push(params[i] - lr * grads[i] / (Math.sqrt(rmspropV[i]) + epsilon));
        }
        break;

      default:
        // 默认SGD
        for (let i = 0; i < params.length; i++) {
          updatedParams.push(params[i] - lr * grads[i]);
        }
    }

    return { parameters: updatedParams, stepSize, gradientNorm };
  }
}

/**
 * 15. 正则化节点
 */
export class RegularizationNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/regularization';
  public static readonly NAME = '正则化';
  public static readonly DESCRIPTION = '各种正则化技术实现';

  constructor() {
    super(RegularizationNode.TYPE, RegularizationNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('parameters', 'array', '参数', []);
    this.addInput('regularizationType', 'string', '正则化类型', 'l2');
    this.addInput('lambda', 'number', '正则化强度', 0.01);
    this.addInput('p', 'number', 'Lp范数的p值', 2);
  }

  private setupOutputs(): void {
    this.addOutput('regularizationLoss', 'number', '正则化损失');
    this.addOutput('regularizationGradient', 'array', '正则化梯度');
    this.addOutput('parameterNorm', 'number', '参数范数');
  }

  public execute(inputs: any): any {
    try {
      const parameters = this.getInputValue(inputs, 'parameters');
      const regularizationType = this.getInputValue(inputs, 'regularizationType');
      const lambda = this.getInputValue(inputs, 'lambda');
      const p = this.getInputValue(inputs, 'p');

      if (!Array.isArray(parameters)) {
        throw new Error('参数无效');
      }

      const result = this.computeRegularization(parameters, regularizationType, lambda, p);

      return {
        regularizationLoss: result.loss,
        regularizationGradient: result.gradient,
        parameterNorm: result.norm,
        result: { 
          status: 'computed', 
          regularizationType,
          lambda,
          parameterCount: parameters.length
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        regularizationLoss: 0,
        regularizationGradient: [],
        parameterNorm: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '正则化计算失败'
      };
    }
  }

  private computeRegularization(params: number[], type: string, lambda: number, p: number): any {
    const gradient: number[] = [];
    let loss = 0;
    let norm = 0;

    switch (type) {
      case 'l1':
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          loss += Math.abs(param);
          gradient.push(Math.sign(param));
          norm += Math.abs(param);
        }
        loss *= lambda;
        gradient.forEach((g, i) => gradient[i] = g * lambda);
        break;

      case 'l2':
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          loss += param * param;
          gradient.push(2 * param);
          norm += param * param;
        }
        loss = 0.5 * lambda * loss;
        gradient.forEach((g, i) => gradient[i] = g * lambda);
        norm = Math.sqrt(norm);
        break;

      case 'elastic_net':
        const l1Ratio = 0.5; // L1和L2的混合比例
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          loss += l1Ratio * Math.abs(param) + (1 - l1Ratio) * param * param;
          gradient.push(l1Ratio * Math.sign(param) + 2 * (1 - l1Ratio) * param);
          norm += param * param;
        }
        loss *= lambda;
        gradient.forEach((g, i) => gradient[i] = g * lambda);
        norm = Math.sqrt(norm);
        break;

      case 'lp':
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          loss += Math.pow(Math.abs(param), p);
          gradient.push(p * Math.pow(Math.abs(param), p - 1) * Math.sign(param));
          norm += Math.pow(Math.abs(param), p);
        }
        loss *= lambda;
        gradient.forEach((g, i) => gradient[i] = g * lambda);
        norm = Math.pow(norm, 1 / p);
        break;

      default:
        // 默认L2正则化
        for (let i = 0; i < params.length; i++) {
          const param = params[i];
          loss += param * param;
          gradient.push(2 * param * lambda);
          norm += param * param;
        }
        loss = 0.5 * lambda * loss;
        norm = Math.sqrt(norm);
    }

    return { loss, gradient, norm };
  }
}

// 导出所有深度学习节点
export const DEEP_LEARNING_NODES = [
  LossFunctionNode,
  OptimizerNode,
  RegularizationNode
] as const;
