/**
 * 批次3.2节点注册表
 * 注册边缘路由节点、云边协调节点、5G网络节点到编辑器
 */

import { NodeRegistry } from './NodeRegistry';

// 导入边缘路由节点
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from '../nodes/edge/EdgeRoutingNodes';

import {
  EdgeOptimizationNode,
  EdgeQoSNode
} from '../nodes/edge/EdgeRoutingNodes2';

// 导入云边协调节点
import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from '../nodes/edge/CloudEdgeNodes';

import {
  DataSynchronizationNode,
  TaskDistributionNode
} from '../nodes/edge/CloudEdgeNodes2';

import {
  ResourceOptimizationNode,
  LatencyOptimizationNode
} from '../nodes/edge/CloudEdgeNodes3';

import {
  BandwidthOptimizationNode,
  CostOptimizationNode
} from '../nodes/edge/CloudEdgeNodes4';

// 导入5G网络节点
import {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from '../nodes/edge/FiveGNetworkNodes';

import {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from '../nodes/edge/FiveGNetworkNodes2';

import {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from '../nodes/edge/FiveGNetworkNodes3';

/**
 * 批次3.2节点注册表类
 */
export class Batch32NodesRegistry {
  private static instance: Batch32NodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registeredNodes: Map<string, any> = new Map();

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): Batch32NodesRegistry {
    if (!Batch32NodesRegistry.instance) {
      Batch32NodesRegistry.instance = new Batch32NodesRegistry();
    }
    return Batch32NodesRegistry.instance;
  }

  /**
   * 注册所有批次3.2节点
   */
  public registerAllNodes(): void {
    this.registerEdgeRoutingNodes();
    this.registerCloudEdgeNodes();
    this.registerFiveGNodes();
    
    console.log('批次3.2节点注册完成');
    console.log(`边缘路由节点：6个`);
    console.log(`云边协调节点：8个`);
    console.log(`5G网络节点：8个`);
    console.log(`总计：22个节点`);
  }

  /**
   * 注册边缘路由节点
   */
  private registerEdgeRoutingNodes(): void {
    // 边缘路由节点
    this.nodeRegistry.registerNode({
      type: 'EdgeRoutingNode',
      name: '边缘路由',
      description: '提供智能边缘路由决策功能',
      category: 'Edge/Routing',
      nodeClass: EdgeRoutingNode,
      icon: 'route',
      color: '#52C41A',
      tags: ['edge', 'routing', 'network', 'optimization']
    });

    // 边缘负载均衡节点
    this.nodeRegistry.registerNode({
      type: 'EdgeLoadBalancingNode',
      name: '边缘负载均衡',
      description: '提供边缘节点间的负载均衡功能',
      category: 'Edge/Routing',
      nodeClass: EdgeLoadBalancingNode,
      icon: 'balance',
      color: '#52C41A',
      tags: ['edge', 'load-balancing', 'performance', 'distribution']
    });

    // 边缘缓存节点
    this.nodeRegistry.registerNode({
      type: 'EdgeCachingNode',
      name: '边缘缓存',
      description: '提供边缘缓存管理功能',
      category: 'Edge/Routing',
      nodeClass: EdgeCachingNode,
      icon: 'database',
      color: '#52C41A',
      tags: ['edge', 'cache', 'storage', 'performance']
    });

    // 边缘压缩节点
    this.nodeRegistry.registerNode({
      type: 'EdgeCompressionNode',
      name: '边缘压缩',
      description: '提供数据压缩和解压缩功能',
      category: 'Edge/Routing',
      nodeClass: EdgeCompressionNode,
      icon: 'compress',
      color: '#52C41A',
      tags: ['edge', 'compression', 'data', 'optimization']
    });

    // 边缘优化节点
    this.nodeRegistry.registerNode({
      type: 'EdgeOptimizationNode',
      name: '边缘优化',
      description: '提供边缘计算资源优化功能',
      category: 'Edge/Routing',
      nodeClass: EdgeOptimizationNode,
      icon: 'optimization',
      color: '#52C41A',
      tags: ['edge', 'optimization', 'resource', 'performance']
    });

    // 边缘服务质量节点
    this.nodeRegistry.registerNode({
      type: 'EdgeQoSNode',
      name: '边缘服务质量',
      description: '提供边缘服务质量管理和监控功能',
      category: 'Edge/Routing',
      nodeClass: EdgeQoSNode,
      icon: 'quality',
      color: '#52C41A',
      tags: ['edge', 'qos', 'quality', 'monitoring']
    });

    console.log('边缘路由节点注册完成 - 6个节点');
  }

  /**
   * 注册云边协调节点
   */
  private registerCloudEdgeNodes(): void {
    // 云边协调节点
    this.nodeRegistry.registerNode({
      type: 'CloudEdgeOrchestrationNode',
      name: '云边协调',
      description: '提供云端和边缘节点之间的协调管理功能',
      category: 'Edge/CloudEdge',
      nodeClass: CloudEdgeOrchestrationNode,
      icon: 'cloud',
      color: '#1890FF',
      tags: ['cloud', 'edge', 'orchestration', 'coordination']
    });

    // 混合计算节点
    this.nodeRegistry.registerNode({
      type: 'HybridComputingNode',
      name: '混合计算',
      description: '提供云端和边缘混合计算能力',
      category: 'Edge/CloudEdge',
      nodeClass: HybridComputingNode,
      icon: 'hybrid',
      color: '#1890FF',
      tags: ['hybrid', 'computing', 'cloud', 'edge']
    });

    // 数据同步节点
    this.nodeRegistry.registerNode({
      type: 'DataSynchronizationNode',
      name: '数据同步',
      description: '提供云端和边缘之间的数据同步功能',
      category: 'Edge/CloudEdge',
      nodeClass: DataSynchronizationNode,
      icon: 'sync',
      color: '#1890FF',
      tags: ['data', 'sync', 'cloud', 'edge']
    });

    // 任务分发节点
    this.nodeRegistry.registerNode({
      type: 'TaskDistributionNode',
      name: '任务分发',
      description: '提供任务在云端和边缘之间的智能分发功能',
      category: 'Edge/CloudEdge',
      nodeClass: TaskDistributionNode,
      icon: 'distribution',
      color: '#1890FF',
      tags: ['task', 'distribution', 'scheduling', 'workload']
    });

    // 资源优化节点
    this.nodeRegistry.registerNode({
      type: 'ResourceOptimizationNode',
      name: '资源优化',
      description: '提供云边资源的智能优化功能',
      category: 'Edge/CloudEdge',
      nodeClass: ResourceOptimizationNode,
      icon: 'optimization',
      color: '#1890FF',
      tags: ['resource', 'optimization', 'efficiency', 'cost']
    });

    // 延迟优化节点
    this.nodeRegistry.registerNode({
      type: 'LatencyOptimizationNode',
      name: '延迟优化',
      description: '提供网络延迟优化功能',
      category: 'Edge/CloudEdge',
      nodeClass: LatencyOptimizationNode,
      icon: 'speed',
      color: '#1890FF',
      tags: ['latency', 'optimization', 'network', 'performance']
    });

    // 带宽优化节点
    this.nodeRegistry.registerNode({
      type: 'BandwidthOptimizationNode',
      name: '带宽优化',
      description: '提供网络带宽优化功能',
      category: 'Edge/CloudEdge',
      nodeClass: BandwidthOptimizationNode,
      icon: 'bandwidth',
      color: '#1890FF',
      tags: ['bandwidth', 'optimization', 'network', 'traffic']
    });

    // 成本优化节点
    this.nodeRegistry.registerNode({
      type: 'CostOptimizationNode',
      name: '成本优化',
      description: '提供云边资源成本优化功能',
      category: 'Edge/CloudEdge',
      nodeClass: CostOptimizationNode,
      icon: 'cost',
      color: '#1890FF',
      tags: ['cost', 'optimization', 'budget', 'efficiency']
    });

    console.log('云边协调节点注册完成 - 8个节点');
  }

  /**
   * 注册5G网络节点
   */
  private registerFiveGNodes(): void {
    // 5G连接节点
    this.nodeRegistry.registerNode({
      type: '5GConnectionNode',
      name: '5G连接',
      description: '提供5G网络连接管理功能',
      category: 'Edge/5G',
      nodeClass: FiveGConnectionNode,
      icon: '5g',
      color: '#722ED1',
      tags: ['5g', 'connection', 'network', 'mobile']
    });

    // 5G网络切片节点
    this.nodeRegistry.registerNode({
      type: '5GSlicingNode',
      name: '5G网络切片',
      description: '提供5G网络切片管理功能',
      category: 'Edge/5G',
      nodeClass: FiveGSlicingNode,
      icon: 'slice',
      color: '#722ED1',
      tags: ['5g', 'slicing', 'network', 'virtualization']
    });

    // 5G服务质量节点
    this.nodeRegistry.registerNode({
      type: '5GQoSNode',
      name: '5G服务质量',
      description: '提供5G网络QoS管理功能',
      category: 'Edge/5G',
      nodeClass: FiveGQoSNode,
      icon: 'quality',
      color: '#722ED1',
      tags: ['5g', 'qos', 'quality', 'service']
    });

    // 5G延迟管理节点
    this.nodeRegistry.registerNode({
      type: '5GLatencyNode',
      name: '5G延迟管理',
      description: '提供5G网络延迟管理和优化功能',
      category: 'Edge/5G',
      nodeClass: FiveGLatencyNode,
      icon: 'speed',
      color: '#722ED1',
      tags: ['5g', 'latency', 'optimization', 'performance']
    });

    // 5G带宽管理节点
    this.nodeRegistry.registerNode({
      type: '5GBandwidthNode',
      name: '5G带宽管理',
      description: '提供5G网络带宽管理和分配功能',
      category: 'Edge/5G',
      nodeClass: FiveGBandwidthNode,
      icon: 'bandwidth',
      color: '#722ED1',
      tags: ['5g', 'bandwidth', 'allocation', 'traffic']
    });

    // 5G安全节点
    this.nodeRegistry.registerNode({
      type: '5GSecurityNode',
      name: '5G安全',
      description: '提供5G网络安全管理功能',
      category: 'Edge/5G',
      nodeClass: FiveGSecurityNode,
      icon: 'security',
      color: '#722ED1',
      tags: ['5g', 'security', 'protection', 'threat']
    });

    // 5G监控节点
    this.nodeRegistry.registerNode({
      type: '5GMonitoringNode',
      name: '5G监控',
      description: '提供5G网络监控和分析功能',
      category: 'Edge/5G',
      nodeClass: FiveGMonitoringNode,
      icon: 'monitoring',
      color: '#722ED1',
      tags: ['5g', 'monitoring', 'analytics', 'metrics']
    });

    // 5G优化节点
    this.nodeRegistry.registerNode({
      type: '5GOptimizationNode',
      name: '5G优化',
      description: '提供5G网络优化和性能调优功能',
      category: 'Edge/5G',
      nodeClass: FiveGOptimizationNode,
      icon: 'optimization',
      color: '#722ED1',
      tags: ['5g', 'optimization', 'performance', 'tuning']
    });

    console.log('5G网络节点注册完成 - 8个节点');
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 边缘路由节点
      'EdgeRoutingNode',
      'EdgeLoadBalancingNode',
      'EdgeCachingNode',
      'EdgeCompressionNode',
      'EdgeOptimizationNode',
      'EdgeQoSNode',

      // 云边协调节点
      'CloudEdgeOrchestrationNode',
      'HybridComputingNode',
      'DataSynchronizationNode',
      'TaskDistributionNode',
      'ResourceOptimizationNode',
      'LatencyOptimizationNode',
      'BandwidthOptimizationNode',
      'CostOptimizationNode',

      // 5G网络节点
      '5GConnectionNode',
      '5GSlicingNode',
      '5GQoSNode',
      '5GLatencyNode',
      '5GBandwidthNode',
      '5GSecurityNode',
      '5GMonitoringNode',
      '5GOptimizationNode'
    ];
  }

  /**
   * 获取节点分类信息
   */
  public getNodeCategories(): Record<string, string[]> {
    return {
      'Edge/Routing': [
        'EdgeRoutingNode',
        'EdgeLoadBalancingNode',
        'EdgeCachingNode',
        'EdgeCompressionNode',
        'EdgeOptimizationNode',
        'EdgeQoSNode'
      ],
      'Edge/CloudEdge': [
        'CloudEdgeOrchestrationNode',
        'HybridComputingNode',
        'DataSynchronizationNode',
        'TaskDistributionNode',
        'ResourceOptimizationNode',
        'LatencyOptimizationNode',
        'BandwidthOptimizationNode',
        'CostOptimizationNode'
      ],
      'Edge/5G': [
        '5GConnectionNode',
        '5GSlicingNode',
        '5GQoSNode',
        '5GLatencyNode',
        '5GBandwidthNode',
        '5GSecurityNode',
        '5GMonitoringNode',
        '5GOptimizationNode'
      ]
    };
  }
}

// 导出单例实例
export const batch32NodesRegistry = Batch32NodesRegistry.getInstance();

// 自动注册所有节点
batch32NodesRegistry.registerAllNodes();
