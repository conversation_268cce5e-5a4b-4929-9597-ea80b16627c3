/**
 * 批次3.3节点编辑器集成
 * 深度学习和机器学习节点在编辑器中的集成
 */

import { NodeEditor } from '../NodeEditor';
import { VisualScriptNode } from '../../../libs/dl-engine-types';
import { batch33NodesRegistry } from '../../../libs/dl-engine';

/**
 * 批次3.3节点编辑器集成类
 */
export class Batch33NodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, any> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeNodes();
  }

  /**
   * 初始化批次3.3节点
   */
  private initializeNodes(): void {
    this.registerDeepLearningNodes();
    this.registerMachineLearningNodes();
    this.setupNodeCategories();
    this.setupNodePalette();
  }

  /**
   * 注册深度学习节点到编辑器
   */
  private registerDeepLearningNodes(): void {
    const deepLearningNodes = [
      'ai/deepLearningModel',
      'ai/neuralNetwork',
      'ai/convolutionalNetwork',
      'ai/recurrentNetwork',
      'ai/transformerModel',
      'ai/ganModel',
      'ai/vaeModel',
      'ai/attentionMechanism',
      'ai/embeddingLayer',
      'ai/dropoutLayer',
      'ai/batchNormalization',
      'ai/activationFunction',
      'ai/lossFunction',
      'ai/optimizer',
      'ai/regularization'
    ];

    deepLearningNodes.forEach(nodeType => {
      const nodeClass = batch33NodesRegistry.getNodeClass(nodeType);
      const metadata = batch33NodesRegistry.getNodeMetadata(nodeType);
      
      if (nodeClass && metadata) {
        this.nodeEditor.registerNodeType(nodeType, {
          nodeClass,
          category: '深度学习',
          displayName: metadata.description,
          icon: metadata.icon,
          color: metadata.color,
          description: metadata.description,
          tags: ['ai', 'deep-learning', 'neural-network'],
          inputs: this.getNodeInputs(nodeType),
          outputs: this.getNodeOutputs(nodeType)
        });

        this.registeredNodes.set(nodeType, { nodeClass, metadata });
      }
    });

    console.log('深度学习节点已注册到编辑器 - 共15个节点');
  }

  /**
   * 注册机器学习节点到编辑器
   */
  private registerMachineLearningNodes(): void {
    const machineLearningNodes = [
      'ml/reinforcementLearning',
      'ml/federatedLearning',
      'ml/transferLearning',
      'ml/modelEnsemble',
      'ml/hyperparameterTuning',
      'ml/modelValidation',
      'ml/crossValidation',
      'ml/featureSelection',
      'ml/dimensionalityReduction',
      'ml/clustering'
    ];

    machineLearningNodes.forEach(nodeType => {
      const nodeClass = batch33NodesRegistry.getNodeClass(nodeType);
      const metadata = batch33NodesRegistry.getNodeMetadata(nodeType);
      
      if (nodeClass && metadata) {
        this.nodeEditor.registerNodeType(nodeType, {
          nodeClass,
          category: '机器学习',
          displayName: metadata.description,
          icon: metadata.icon,
          color: metadata.color,
          description: metadata.description,
          tags: ['ai', 'machine-learning', 'ml'],
          inputs: this.getNodeInputs(nodeType),
          outputs: this.getNodeOutputs(nodeType)
        });

        this.registeredNodes.set(nodeType, { nodeClass, metadata });
      }
    });

    console.log('机器学习节点已注册到编辑器 - 共10个节点');
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    this.nodeEditor.addNodeCategory({
      id: 'deep-learning',
      name: '深度学习',
      icon: 'brain',
      color: '#FF6B35',
      description: '深度学习相关节点，包括神经网络、CNN、RNN、Transformer等',
      order: 100
    });

    this.nodeEditor.addNodeCategory({
      id: 'machine-learning',
      name: '机器学习',
      icon: 'cpu',
      color: '#66BB6A',
      description: '机器学习相关节点，包括强化学习、联邦学习、模型验证等',
      order: 101
    });
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 深度学习节点组
    this.nodeEditor.addNodeGroup({
      category: 'deep-learning',
      name: '基础模型',
      nodes: [
        'ai/deepLearningModel',
        'ai/neuralNetwork',
        'ai/convolutionalNetwork',
        'ai/recurrentNetwork'
      ]
    });

    this.nodeEditor.addNodeGroup({
      category: 'deep-learning',
      name: '高级模型',
      nodes: [
        'ai/transformerModel',
        'ai/ganModel',
        'ai/vaeModel',
        'ai/attentionMechanism'
      ]
    });

    this.nodeEditor.addNodeGroup({
      category: 'deep-learning',
      name: '网络层',
      nodes: [
        'ai/embeddingLayer',
        'ai/dropoutLayer',
        'ai/batchNormalization',
        'ai/activationFunction'
      ]
    });

    this.nodeEditor.addNodeGroup({
      category: 'deep-learning',
      name: '训练组件',
      nodes: [
        'ai/lossFunction',
        'ai/optimizer',
        'ai/regularization'
      ]
    });

    // 机器学习节点组
    this.nodeEditor.addNodeGroup({
      category: 'machine-learning',
      name: '高级算法',
      nodes: [
        'ml/reinforcementLearning',
        'ml/federatedLearning',
        'ml/transferLearning',
        'ml/modelEnsemble'
      ]
    });

    this.nodeEditor.addNodeGroup({
      category: 'machine-learning',
      name: '模型优化',
      nodes: [
        'ml/hyperparameterTuning',
        'ml/modelValidation',
        'ml/crossValidation'
      ]
    });

    this.nodeEditor.addNodeGroup({
      category: 'machine-learning',
      name: '数据处理',
      nodes: [
        'ml/featureSelection',
        'ml/dimensionalityReduction',
        'ml/clustering'
      ]
    });
  }

  /**
   * 获取节点输入定义
   */
  private getNodeInputs(nodeType: string): any[] {
    const nodeClass = batch33NodesRegistry.getNodeClass(nodeType);
    if (!nodeClass) return [];

    // 创建临时实例获取输入定义
    const tempNode = new nodeClass();
    return tempNode.getInputs ? tempNode.getInputs() : [];
  }

  /**
   * 获取节点输出定义
   */
  private getNodeOutputs(nodeType: string): any[] {
    const nodeClass = batch33NodesRegistry.getNodeClass(nodeType);
    if (!nodeClass) return [];

    // 创建临时实例获取输出定义
    const tempNode = new nodeClass();
    return tempNode.getOutputs ? tempNode.getOutputs() : [];
  }

  /**
   * 创建节点实例
   */
  public createNode(nodeType: string): VisualScriptNode | null {
    return batch33NodesRegistry.createNode(nodeType);
  }

  /**
   * 获取已注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStats(): any {
    return {
      totalNodes: this.registeredNodes.size,
      deepLearningNodes: 15,
      machineLearningNodes: 10,
      categories: ['深度学习', '机器学习']
    };
  }

  /**
   * 验证节点集成
   */
  public validateIntegration(): boolean {
    const expectedNodes = 25;
    const actualNodes = this.registeredNodes.size;
    
    if (actualNodes !== expectedNodes) {
      console.error(`节点集成验证失败: 期望${expectedNodes}个节点，实际${actualNodes}个节点`);
      return false;
    }

    // 验证每个节点都能正常创建
    for (const nodeType of this.registeredNodes.keys()) {
      const node = this.createNode(nodeType);
      if (!node) {
        console.error(`节点创建失败: ${nodeType}`);
        return false;
      }
    }

    console.log('批次3.3节点集成验证成功 - 25个节点全部正常');
    return true;
  }

  /**
   * 获取节点使用示例
   */
  public getNodeExamples(): any {
    return {
      deepLearning: {
        'ai/deepLearningModel': {
          description: '创建一个简单的前馈神经网络',
          inputs: {
            modelId: 'mnist-classifier',
            modelType: 'feedforward',
            inputSize: 784,
            outputSize: 10,
            hiddenLayers: [128, 64],
            activation: 'relu'
          }
        },
        'ai/convolutionalNetwork': {
          description: '图像分类的卷积神经网络',
          inputs: {
            inputImage: '28x28像素图像数据',
            filters: '卷积核配置',
            kernelSize: 3,
            stride: 1,
            padding: 'same'
          }
        }
      },
      machineLearning: {
        'ml/reinforcementLearning': {
          description: 'Q-learning强化学习示例',
          inputs: {
            state: 'current_state',
            availableActions: ['up', 'down', 'left', 'right'],
            learningRate: 0.1,
            discountFactor: 0.9
          }
        },
        'ml/clustering': {
          description: 'K-means聚类分析',
          inputs: {
            inputData: '二维数据点数组',
            clusteringMethod: 'kmeans',
            numClusters: 3,
            maxIterations: 100
          }
        }
      }
    };
  }
}
