# 边缘计算节点API参考

## 概述

本文档提供DL引擎边缘计算节点的详细API参考，包括所有输入参数、输出结果和方法签名。

## 基础接口

### BaseNode

所有边缘计算节点都继承自BaseNode基类：

```typescript
abstract class BaseNode {
  type: string;
  name: string;
  category: string;
  inputs: NodeInput[];
  outputs: NodeOutput[];
  
  abstract execute(inputs: Record<string, any>): Promise<Record<string, any>>;
}
```

### NodeInput

```typescript
interface NodeInput {
  name: string;
  type: string;
  label: string;
  required?: boolean;
  defaultValue?: any;
  description?: string;
}
```

### NodeOutput

```typescript
interface NodeOutput {
  name: string;
  type: string;
  label: string;
  description?: string;
}
```

## 边缘路由节点API

### EdgeRoutingNode

```typescript
class EdgeRoutingNode extends BaseNode {
  static TYPE = 'EdgeRoutingNode';
  static NAME = '边缘路由';
  static DESCRIPTION = '提供智能边缘路由决策功能';

  inputs: [
    { name: 'clientInfo', type: 'object', label: '客户端信息' },
    { name: 'routingPolicy', type: 'string', label: '路由策略' },
    { name: 'edgeNodes', type: 'array', label: '边缘节点列表' },
    { name: 'networkMetrics', type: 'object', label: '网络指标' }
  ];

  outputs: [
    { name: 'selectedNode', type: 'object', label: '选中节点' },
    { name: 'routingDecision', type: 'object', label: '路由决策' },
    { name: 'routingMetrics', type: 'object', label: '路由指标' }
  ];

  async execute(inputs: {
    clientInfo: {
      location?: { latitude: number; longitude: number };
      deviceType?: string;
      userAgent?: string;
    };
    routingPolicy: 'latency' | 'load' | 'cost' | 'distance';
    edgeNodes: Array<{
      nodeId: string;
      status: 'active' | 'inactive' | 'maintenance';
      location?: { latitude: number; longitude: number };
      latency?: number;
      load?: number;
      cost?: number;
      capacity?: number;
    }>;
    networkMetrics: Record<string, any>;
  }): Promise<{
    selectedNode: {
      nodeId: string;
      score: number;
      reason: string;
    };
    routingDecision: {
      policy: string;
      factors: Record<string, number>;
      alternatives: any[];
    };
    routingMetrics: {
      decisionTime: number;
      candidateCount: number;
      averageScore: number;
    };
  }>;
}
```

### EdgeLoadBalancingNode

```typescript
class EdgeLoadBalancingNode extends BaseNode {
  static TYPE = 'EdgeLoadBalancingNode';
  static NAME = '边缘负载均衡';

  async execute(inputs: {
    edgeNodes: Array<{
      nodeId: string;
      activeConnections?: number;
      cpuUsage?: number;
      memoryUsage?: number;
      weight?: number;
    }>;
    balancingAlgorithm: 'round_robin' | 'least_connections' | 'weighted_round_robin' | 'ip_hash';
    requestInfo: {
      clientIP?: string;
      sessionId?: string;
      requestType?: string;
    };
    healthChecks: Record<string, {
      status: 'healthy' | 'unhealthy' | 'unknown';
      lastCheck?: number;
      responseTime?: number;
    }>;
  }): Promise<{
    targetNode: {
      nodeId: string;
      reason: string;
      expectedLoad: number;
    };
    loadDistribution: Record<string, {
      currentLoad: number;
      projectedLoad: number;
      utilization: number;
    }>;
    balancingMetrics: {
      algorithm: string;
      selectionTime: number;
      healthyNodes: number;
      totalNodes: number;
    };
  }>;
}
```

### EdgeCachingNode

```typescript
class EdgeCachingNode extends BaseNode {
  static TYPE = 'EdgeCachingNode';
  static NAME = '边缘缓存';

  async execute(inputs: {
    cacheKey: string;
    cacheValue?: any;
    operation: 'get' | 'set' | 'delete' | 'exists' | 'clear';
    ttl?: number;
    cacheStrategy?: 'lru' | 'lfu' | 'fifo' | 'ttl';
  }): Promise<{
    result: any;
    cacheHit: boolean;
    cacheStats: {
      hitRate: number;
      missRate: number;
      totalRequests: number;
      cacheSize: number;
      memoryUsage: number;
    };
  }>;
}
```

## 云边协调节点API

### CloudEdgeOrchestrationNode

```typescript
class CloudEdgeOrchestrationNode extends BaseNode {
  static TYPE = 'CloudEdgeOrchestrationNode';
  static NAME = '云边协调';

  async execute(inputs: {
    cloudResources: Array<{
      id: string;
      type: 'cloud';
      cpuCores: number;
      memory: number;
      storage?: number;
      bandwidth?: number;
      cost?: number;
    }>;
    edgeNodes: Array<{
      id: string;
      type: 'edge';
      cpuCores: number;
      memory: number;
      storage?: number;
      bandwidth?: number;
      latency?: number;
      location?: { latitude: number; longitude: number };
    }>;
    workloads: Array<{
      id: string;
      type?: 'realTime' | 'compute' | 'storage' | 'network' | 'ai';
      cpuRequirement: number;
      memoryRequirement: number;
      storageRequirement?: number;
      latencyRequirement?: number;
      priority?: 'high' | 'medium' | 'low';
    }>;
    orchestrationPolicy: {
      strategy?: 'optimal' | 'cost' | 'latency' | 'balanced';
      constraints?: Record<string, any>;
    };
  }): Promise<{
    orchestrationPlan: {
      phases: Array<{
        phase: number;
        workloads: string[];
        resources: string[];
        estimatedTime: number;
      }>;
      totalDuration: number;
      resourceUtilization: Record<string, number>;
    };
    resourceAllocation: Record<string, {
      workloads: string[];
      utilization: {
        cpu: number;
        memory: number;
        storage: number;
      };
    }>;
    orchestrationMetrics: {
      workloadAllocation: number;
      resourceEfficiency: number;
      unallocatedWorkloads: number;
      totalCost: number;
    };
  }>;
}
```

### HybridComputingNode

```typescript
class HybridComputingNode extends BaseNode {
  static TYPE = 'HybridComputingNode';
  static NAME = '混合计算';

  async execute(inputs: {
    computeTask: {
      id: string;
      type: 'data_processing' | 'machine_learning' | 'image_processing' | 'video_processing';
      dataSize?: number;
      cpuRequirement: number;
      memoryRequirement: number;
      estimatedTime: number;
      requiresGPU?: boolean;
    };
    cloudCapacity: {
      cpuCores: number;
      memory: number;
      hasGPU?: boolean;
      bandwidth: number;
      latency: number;
      costPerHour: number;
    };
    edgeCapacity: {
      cpuCores: number;
      memory: number;
      hasGPU?: boolean;
      bandwidth: number;
      latency: number;
      costPerHour: number;
    };
    hybridStrategy: 'optimal' | 'latency_first' | 'cost_first' | 'performance_first';
  }): Promise<{
    computePlan: {
      executionOrder: Array<{
        taskId: string;
        location: 'edge' | 'cloud';
        priority: number;
        startTime: number;
      }>;
      resourceAllocation: {
        edge: { allocatedCpu: number; allocatedMemory: number; utilization: number };
        cloud: { allocatedCpu: number; allocatedMemory: number; utilization: number };
      };
      synchronizationPoints: any[];
      fallbackStrategy: any;
    };
    taskDistribution: {
      edge: any[];
      cloud: any[];
      hybrid: any[];
    };
    computeMetrics: {
      taskDistribution: {
        edge: number;
        cloud: number;
        edgeRatio: number;
        cloudRatio: number;
      };
      performance: {
        originalTime: number;
        estimatedTime: number;
        speedup: string;
        efficiency: string;
      };
      resourceUtilization: {
        edge: number;
        cloud: number;
      };
    };
  }>;
}
```

## 5G网络节点API

### 5GConnectionNode

```typescript
class FiveGConnectionNode extends BaseNode {
  static TYPE = '5GConnectionNode';
  static NAME = '5G连接';

  async execute(inputs: {
    deviceInfo: {
      supportedNetworkTypes: string[];
      fiveGCapabilities?: {
        eMBB?: boolean;
        URLLC?: boolean;
        mMTC?: boolean;
      };
      location?: { latitude: number; longitude: number };
    };
    connectionType: 'eMBB' | 'URLLC' | 'mMTC';
    networkSlice: string;
    qosRequirements: {
      minBandwidth?: number;
      maxLatency?: number;
      reliability?: number;
      priority?: string;
    };
  }): Promise<{
    connectionStatus: {
      state: 'connected' | 'connecting' | 'failed';
      baseStation: string;
      signalStrength: number;
      connectionType: string;
      networkSlice: string;
      timestamp: number;
    };
    networkMetrics: {
      signalStrength: number;
      bandwidth: {
        downlink: number;
        uplink: number;
        utilization: number;
      };
      latency: {
        current: number;
        average: number;
        jitter: number;
      };
      packetLoss: number;
      throughput: {
        downlink: number;
        uplink: number;
      };
    };
    connectionId: string;
  }>;
}
```

### 5GSlicingNode

```typescript
class FiveGSlicingNode extends BaseNode {
  static TYPE = '5GSlicingNode';
  static NAME = '5G网络切片';

  async execute(inputs: {
    sliceRequirements: {
      bandwidth: number;
      maxLatency?: number;
      reliability?: number;
      maxConnections?: number;
      duration?: string;
      autoScale?: boolean;
    };
    networkResources: {
      availableBandwidth: number;
      minLatency: number;
      reliability: number;
      maxConnections: number;
    };
    sliceType: 'eMBB' | 'URLLC' | 'mMTC';
    tenantInfo: {
      id: string;
      name?: string;
      priority?: string;
    };
  }): Promise<{
    sliceId: string;
    sliceConfiguration: {
      sliceType: string;
      tenant: {
        id: string;
        name: string;
        priority: string;
      };
      sla: {
        bandwidth: number;
        latency: number;
        reliability: number;
        availability: number;
      };
      security: {
        isolation: string;
        encryption: string;
        authentication: string;
      };
      qos: {
        trafficClass: string;
        priority: number;
        scheduling: string;
      };
      lifecycle: {
        duration: string;
        autoScale: boolean;
        monitoring: boolean;
      };
    };
    resourceAllocation: {
      bandwidth: {
        guaranteed: number;
        maximum: number;
        priority: string;
      };
      latency: {
        target: number;
        guaranteed: number;
      };
      connections: {
        maximum: number;
        concurrent: number;
      };
      compute: {
        cpu: number;
        memory: number;
        storage: number;
      };
      network: {
        uplink: number;
        downlink: number;
      };
    };
  }>;
}
```

## 错误处理

所有节点都可能抛出以下类型的错误：

```typescript
interface NodeError extends Error {
  code: string;
  nodeType: string;
  details?: Record<string, any>;
}
```

常见错误代码：
- `INVALID_INPUT`: 输入参数无效
- `RESOURCE_UNAVAILABLE`: 资源不可用
- `NETWORK_ERROR`: 网络错误
- `TIMEOUT`: 操作超时
- `CONFIGURATION_ERROR`: 配置错误

## 类型定义

```typescript
// 位置信息
interface Location {
  latitude: number;
  longitude: number;
}

// 资源信息
interface Resource {
  id: string;
  type: string;
  cpuCores: number;
  memory: number;
  storage?: number;
  bandwidth?: number;
}

// QoS要求
interface QoSRequirements {
  minBandwidth?: number;
  maxLatency?: number;
  reliability?: number;
  priority?: string;
}

// 网络指标
interface NetworkMetrics {
  latency: number;
  bandwidth: number;
  packetLoss: number;
  jitter: number;
  throughput: number;
}
```

## 版本信息

- API版本: 1.0.0
- 最后更新: 2024-01-15
- 兼容性: DL引擎 v2.0+
