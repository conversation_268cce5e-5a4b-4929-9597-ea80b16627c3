/**
 * 批次3.3节点导出
 * 深度学习和机器学习节点
 */

// 深度学习节点导出
export {
  DeepLearningModelNode,
  NeuralNetworkNode,
  ConvolutionalNetworkNode,
  RecurrentNetworkNode
} from '../ai/DeepLearningNodes';

export {
  TransformerModelNode,
  GANModelNode
} from '../ai/DeepLearningNodes2';

export {
  VAEModelNode,
  AttentionMechanismNode
} from '../ai/DeepLearningNodes3';

export {
  EmbeddingLayerNode,
  DropoutLayerNode,
  BatchNormalizationNode,
  ActivationFunctionNode
} from '../ai/DeepLearningNodes4';

export {
  LossFunctionNode,
  OptimizerNode,
  RegularizationNode,
  DEEP_LEARNING_NODES
} from '../ai/DeepLearningNodes5';

// 机器学习节点导出
export {
  ReinforcementLearningNode,
  FederatedLearningNode
} from '../ai/MachineLearningNodes';

export {
  TransferLearningNode,
  ModelEnsembleNode
} from '../ai/MachineLearningNodes2';

export {
  HyperparameterTuningNode,
  ModelValidationNode
} from '../ai/MachineLearningNodes3';

export {
  CrossValidationNode,
  FeatureSelectionNode,
  DimensionalityReductionNode,
  ClusteringNode,
  MACHINE_LEARNING_NODES_4
} from '../ai/MachineLearningNodes4';

// 注册表导出
export { Batch33NodesRegistry, batch33NodesRegistry } from '../../registry/Batch33NodesRegistry';

// 节点分类常量
export const BATCH33_NODE_CATEGORIES = {
  DEEP_LEARNING: '深度学习',
  MACHINE_LEARNING: '机器学习'
} as const;

// 深度学习节点类型
export const DEEP_LEARNING_NODE_TYPES = [
  'ai/deepLearningModel',
  'ai/neuralNetwork',
  'ai/convolutionalNetwork',
  'ai/recurrentNetwork',
  'ai/transformerModel',
  'ai/ganModel',
  'ai/vaeModel',
  'ai/attentionMechanism',
  'ai/embeddingLayer',
  'ai/dropoutLayer',
  'ai/batchNormalization',
  'ai/activationFunction',
  'ai/lossFunction',
  'ai/optimizer',
  'ai/regularization'
] as const;

// 机器学习节点类型
export const MACHINE_LEARNING_NODE_TYPES = [
  'ml/reinforcementLearning',
  'ml/federatedLearning',
  'ml/transferLearning',
  'ml/modelEnsemble',
  'ml/hyperparameterTuning',
  'ml/modelValidation',
  'ml/crossValidation',
  'ml/featureSelection',
  'ml/dimensionalityReduction',
  'ml/clustering'
] as const;

// 所有批次3.3节点类型
export const BATCH33_NODE_TYPES = [
  ...DEEP_LEARNING_NODE_TYPES,
  ...MACHINE_LEARNING_NODE_TYPES
] as const;

// 节点统计信息
export const BATCH33_STATS = {
  DEEP_LEARNING_COUNT: 15,
  MACHINE_LEARNING_COUNT: 10,
  TOTAL_COUNT: 25,
  CATEGORIES: 2
} as const;

// 已实现的节点类型
export const IMPLEMENTED_NODE_TYPES = [
  // 深度学习节点（15个）- 全部实现
  'ai/deepLearningModel',
  'ai/neuralNetwork',
  'ai/convolutionalNetwork',
  'ai/recurrentNetwork',
  'ai/transformerModel',
  'ai/ganModel',
  'ai/vaeModel',
  'ai/attentionMechanism',
  'ai/embeddingLayer',
  'ai/dropoutLayer',
  'ai/batchNormalization',
  'ai/activationFunction',
  'ai/lossFunction',
  'ai/optimizer',
  'ai/regularization',

  // 机器学习节点（10个）- 全部实现
  'ml/reinforcementLearning',
  'ml/federatedLearning',
  'ml/transferLearning',
  'ml/modelEnsemble',
  'ml/hyperparameterTuning',
  'ml/modelValidation',
  'ml/crossValidation',
  'ml/featureSelection',
  'ml/dimensionalityReduction',
  'ml/clustering'
] as const;

// 节点功能描述
export const NODE_DESCRIPTIONS = {
  // 深度学习节点
  'ai/deepLearningModel': '创建和管理深度学习模型',
  'ai/neuralNetwork': '通用神经网络实现',
  'ai/convolutionalNetwork': 'CNN卷积神经网络实现',
  'ai/recurrentNetwork': 'RNN循环神经网络实现',
  'ai/transformerModel': 'Transformer架构实现',
  'ai/ganModel': 'GAN生成对抗网络实现',
  'ai/vaeModel': 'VAE变分自编码器实现',
  'ai/attentionMechanism': '注意力机制实现',
  'ai/embeddingLayer': '词嵌入和特征嵌入层',
  'ai/dropoutLayer': 'Dropout正则化层',
  'ai/batchNormalization': '批量归一化层',
  'ai/activationFunction': '各种激活函数实现',
  'ai/lossFunction': '各种损失函数实现',
  'ai/optimizer': '各种优化器实现',
  'ai/regularization': '各种正则化技术实现',

  // 机器学习节点
  'ml/reinforcementLearning': '强化学习算法实现',
  'ml/federatedLearning': '联邦学习协调和聚合',
  'ml/transferLearning': '迁移学习模型适配',
  'ml/modelEnsemble': '多模型集成和投票',
  'ml/hyperparameterTuning': '自动超参数优化',
  'ml/modelValidation': '模型性能验证和评估',
  'ml/crossValidation': 'K折交叉验证',
  'ml/featureSelection': '自动特征选择和重要性评估',
  'ml/dimensionalityReduction': '数据降维和特征压缩',
  'ml/clustering': '无监督聚类算法'
} as const;

// 节点使用场景
export const NODE_USE_CASES = {
  // 深度学习应用场景
  DEEP_LEARNING: [
    '图像识别和分类',
    '自然语言处理',
    '语音识别和合成',
    '计算机视觉',
    '生成式AI',
    '推荐系统',
    '时序数据分析',
    '异常检测'
  ],

  // 机器学习应用场景
  MACHINE_LEARNING: [
    '智能决策系统',
    '数据挖掘和分析',
    '预测建模',
    '模式识别',
    '自动化优化',
    '风险评估',
    '客户细分',
    '质量控制'
  ]
} as const;

// 开发完成状态
export const DEVELOPMENT_STATUS = {
  BATCH: '3.3',
  STATUS: 'COMPLETED',
  COMPLETION_DATE: '2025-07-03',
  DEEP_LEARNING_NODES: 'COMPLETED',
  MACHINE_LEARNING_NODES: 'COMPLETED',
  INTEGRATION: 'IN_PROGRESS',
  TESTING: 'PENDING',
  DOCUMENTATION: 'PENDING'
} as const;

// 导出验证函数
export function validateBatch33Implementation(): boolean {
  const expectedCount = BATCH33_STATS.TOTAL_COUNT;
  const implementedCount = IMPLEMENTED_NODE_TYPES.length;

  return implementedCount === expectedCount;
}

// 导出节点创建工厂函数
export function createBatch33Node(nodeType: string): any {
  return batch33NodesRegistry.createNode(nodeType);
}

// 导出节点类型检查函数
export function isBatch33NodeType(nodeType: string): boolean {
  return (BATCH33_NODE_TYPES as readonly string[]).includes(nodeType);
}

// 导出深度学习节点检查函数
export function isDeepLearningNode(nodeType: string): boolean {
  return (DEEP_LEARNING_NODE_TYPES as readonly string[]).includes(nodeType);
}

// 导出机器学习节点检查函数
export function isMachineLearningNode(nodeType: string): boolean {
  return (MACHINE_LEARNING_NODE_TYPES as readonly string[]).includes(nodeType);
}
